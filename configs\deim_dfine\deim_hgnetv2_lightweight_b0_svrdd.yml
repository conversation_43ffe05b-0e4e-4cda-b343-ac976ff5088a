__include__: [
  './dfine_hgnetv2_s_coco.yml',
  '../base/deim.yml',
  '../dataset/svrdd_detection.yml'
]

print_freq: 100
output_dir: /hy-tmp/outputs/deim_lightweight_b0_svrdd

epoches: 150

# ===== 轻量级增强版配置 =====

# 使用简洁版增强HGNetV2-B0
DEIM:
  backbone: HGNetv2SimpleEnhanced  # 简洁版增强

# 简洁版增强HGNetV2配置
HGNetv2SimpleEnhanced:
  name: 'B0'  # 使用B0规模
  return_idx: [1, 2, 3]
  freeze_at: -1
  freeze_norm: False
  use_lab: True
  
  # 简洁增强配置
  enhance_stages: [2, 3]      # 只增强后两个stage

# ===== 其他配置与原始保持完全一致（控制变量） =====

optimizer:
  type: AdamW
  params: 
    - 
      params: '^(?=.*backbone)(?!.*bn).*'
      lr: 0.0005
    - 
      params: '^(?=.*(?:norm|bn)).*'     # except bias
      weight_decay: 0.
  lr: 0.0005
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# batch size - 减少以节省GPU内存
train_dataloader:
  total_batch_size: 8  # 从16减少到8
  collate_fn:
    mixup_epochs: [10, 130]    # 修复：在第130轮结束mixup
    stop_epoch: 130            # 修复：在第130轮停止数据增强

val_dataloader:
  total_batch_size: 8  # 同样减少验证batch size
  num_workers: 2
  shuffle: False

lrsheduler: flatcosine
lr_gamma: 0.5
warmup_iter: 50

# 早停配置
early_stopping:
  monitor: val_loss
  patience: 10
  mode: min
  min_delta: 0.0001
  verbose: True