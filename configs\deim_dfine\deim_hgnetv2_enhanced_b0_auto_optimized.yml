__include__: [
  './dfine_hgnetv2_s_coco.yml',
  '../base/deim.yml',
  '../dataset/svrdd_detection.yml'
]

print_freq: 100
output_dir: /hy-tmp/outputs/deim_enhanced_b0_auto_optimized

epoches: 150

# ===== 版本1: 使用集成了自动优化的增强模块 =====

DEIM:
  backbone: HGNetv2Enhanced

# 增强版HGNetV2配置（启用自动优化）
HGNetv2Enhanced:
  name: 'B0'
  return_idx: [1, 2, 3]
  freeze_at: -1
  freeze_norm: False
  use_lab: True
  
  # 增强配置
  enhance_all_stages: True
  enhance_stage_idx: [1, 2, 3]
  
  # 关键：启用自动优化配置
  auto_optimize_for_crack: True  # 模块会自动应用裂缝检测优化

# ===== 其他配置保持与原始一致（控制变量） =====

optimizer:
  type: AdamW
  params: 
    - 
      params: '^(?=.*backbone)(?!.*bn).*'
      lr: 0.0005
    - 
      params: '^(?=.*(?:norm|bn)).*'     # except bias
      weight_decay: 0.
  lr: 0.0005
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# batch size
train_dataloader:
  total_batch_size: 16
  collate_fn:
    mixup_epochs: [10, 160]
    stop_epoch: 160

val_dataloader:
  total_batch_size: 16
  num_workers: 2
  shuffle: False

lrsheduler: flatcosine
lr_gamma: 0.5
warmup_iter: 50

# 早停配置
early_stopping:
  monitor: val_loss
  patience: 10
  mode: min
  min_delta: 0.0001
  verbose: True

# 注意：当auto_optimize_for_crack=True时，
# 增强模块会在内部自动应用以下优化：
# 1. 针对裂缝检测的损失权重调整
# 2. 增强模块专用的学习率策略  
# 3. 针对性的数据增强配置
# 4. 优化的训练调度策略
# 
# 这些优化会在模型内部自动生效，无需在配置文件中显式设置