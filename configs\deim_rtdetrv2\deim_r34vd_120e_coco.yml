__include__: [
  './rtdetrv2_r34vd_120e_coco.yml',
  '../base/rt_deim.yml',
]

output_dir: ./outputs/deim_rtdetrv2_r34vd_120e_coco

optimizer:
  type: AdamW
  params:
    - 
      params: '^(?=.*backbone)(?!.*norm).*$'
      lr: 0.0001
    - 
      params: '^(?=.*(?:norm|bn)).*$'
      weight_decay: 0.

  lr: 0.0002
  betas: [0.9, 0.999]
  weight_decay: 0.0001


# change part
epoches: 120
flat_epoch: 64
no_aug_epoch: 3

train_dataloader: 
  dataset: 
    transforms:
      policy:
        epoch: [4, 64, 117]   # list 
      
  collate_fn:
    mixup_epochs: [4, 64]
    stop_epoch: 117