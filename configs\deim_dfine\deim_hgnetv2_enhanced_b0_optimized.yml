__include__: [
  './dfine_hgnetv2_s_coco.yml',
  '../base/deim.yml',
  '../dataset/svrdd_detection.yml'
]

print_freq: 100
output_dir: /hy-tmp/outputs/deim_enhanced_b0_optimized

epoches: 150

# ===== 使用增强版HGNetV2-B0 =====
DEIM:
  backbone: HGNetv2Enhanced

# 增强版HGNetV2配置（基础配置与控制变量版一致）
HGNetv2Enhanced:
  name: 'B0'
  return_idx: [1, 2, 3]
  freeze_at: -1
  freeze_norm: False
  use_lab: True
  
  # 增强配置
  enhance_all_stages: True
  enhance_stage_idx: [1, 2, 3]
  auto_optimize_for_crack: True  # 启用自动优化

# ===== 核心改进1: 针对裂缝检测深度定制的损失权重 =====
DEIMCriterion:
  weight_dict: {
    loss_vfl: 1.0,      # 分类损失保持标准
    loss_bbox: 8.0,     # 大幅增加边界框损失权重（裂缝定位极其重要）
    loss_giou: 3.0,     # 增加GIoU损失权重（形状匹配对裂缝很重要）
    loss_fgl: 0.25,     # 微调焦点损失
    loss_ddf: 2.0       # 增加密集检测损失（裂缝密集分布）
  }
  losses: ['vfl', 'boxes', 'local']
  alpha: 0.8           # 调整focal loss alpha（更关注难样本，裂缝通常是难样本）
  gamma: 2.2           # 调整focal loss gamma（进一步突出难样本）
  reg_max: 32

  matcher:
    type: HungarianMatcher
    weight_dict: {
      cost_class: 2, 
      cost_bbox: 8,     # 匹配时也更重视边界框精度
      cost_giou: 3      # 匹配时重视IoU（裂缝形状匹配）
    }
    alpha: 0.25
    gamma: 2.0

# ===== 核心改进2: 为增强模块设计的专门学习率策略 =====
optimizer:
  type: AdamW
  params: 
    - 
      params: '^(?=.*backbone)(?!.*norm|bn|enhancement).*'  # 排除增强模块
      lr: 0.0003  # backbone基础学习率稍微降低，更稳定训练
    - 
      params: '^(?=.*backbone)(?=.*norm|bn)(?!.*enhancement).*'
      lr: 0.0003
      weight_decay: 0.
    # 关键：增强模块使用专门的学习率策略
    -
      params: '^(?=.*enhancement).*'  # 所有增强模块参数
      lr: 0.0008  # 增强模块需要更高学习率来快速学习裂缝特征
      weight_decay: 0.00005  # 轻微正则化防止过拟合
    -
      params: '^(?=.*(?:encoder|decoder))(?=.*(?:norm|bn|bias)).*'
      weight_decay: 0.
      
  lr: 0.0005
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# ===== 核心改进3: 针对性的数据增强策略 =====
train_dataloader:
  total_batch_size: 16
  collate_fn:
    mixup_epochs: [8, 130]    # 调整mixup时机：给增强模块更多纯净样本学习时间
    stop_epoch: 130           # 提前停止数据增强，让模型在后期专注学习
  
  dataset:
    transforms:
      ops:
        # 针对裂缝检测的专门数据增强
        - {type: RandomBrightnessContrast, brightness_limit: 0.2, contrast_limit: 0.3, p: 0.4}  # 模拟不同光照
        - {type: RandomGamma, gamma_limit: [85, 115], p: 0.3}  # 伽马校正增强对比度
        - {type: GaussNoise, var_limit: [5, 25], p: 0.2}      # 添加噪声提高鲁棒性
        - {type: RandomRotate90, p: 0.3}                       # 旋转增强（裂缝方向多样）
        - {type: HorizontalFlip, p: 0.5}                       # 水平翻转
        - {type: VerticalFlip, p: 0.3}                         # 垂直翻转（裂缝检测特有）

val_dataloader:
  total_batch_size: 16
  num_workers: 2
  shuffle: False

# ===== 核心改进4: 优化的训练调度策略 =====
lrsheduler: flatcosine
lr_gamma: 0.3          # 更激进的学习率衰减（从0.5改为0.3）
warmup_iter: 80        # 更长的warmup（从50改为80），让增强模块充分预热

# 早停配置优化
early_stopping:
  monitor: val_loss
  patience: 15          # 增强模块需要更多时间收敛（从10改为15）
  mode: min
  min_delta: 0.00005    # 更精细的停止条件（从0.0001改为0.00005）
  verbose: True

# ===== 其他优化配置 =====

# 评估配置
eval_spatial_size: [640, 640]  # 裂缝检测需要高分辨率

# 模型保存策略
checkpoint_freq: 8  # 更频繁保存（从默认改为8），防止增强模块训练中断

# 混合精度训练优化
use_amp: True  # 启用自动混合精度，加速训练

# 梯度裁剪（防止增强模块训练不稳定）
clip_grad_norm: 1.0

# 日志配置
log_config:
  # 记录增强模块的特殊指标
  track_enhancement_weights: True  # 跟踪enhancement_weight参数变化
  track_loss_components: True      # 详细记录各损失分量