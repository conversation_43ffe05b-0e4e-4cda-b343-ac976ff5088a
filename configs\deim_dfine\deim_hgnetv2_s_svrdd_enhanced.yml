__include__: [
  './dfine_hgnetv2_s_coco.yml',
  '../base/deim.yml',
  '../dataset/svrdd_detection.yml'
]

print_freq: 100
output_dir: /hy-tmp/outputs/deim_enhanced_aifi

epoches: 150

# 解决增强AIFI中未使用参数的问题
find_unused_parameters: True

# 使用增强版HybridEncoderEnhanced替代原始的HybridEncoder
DEIM:
  backbone: HGNetv2
  encoder: HybridEncoderEnhanced  # 增强版AIFI编码器
  decoder: DFINETransformer

# HybridEncoderEnhanced配置 - 增强版AIFI参数
HybridEncoderEnhanced:
  # 基础参数 - 与原始配置保持一致
  in_channels: [256, 512, 1024]
  feat_strides: [8, 16, 32]
  hidden_dim: 256
  depth_mult: 0.34
  expansion: 0.5
  nhead: 8
  dim_feedforward: 1024
  dropout: 0.0
  enc_act: 'gelu'
  use_encoder_idx: [2]
  num_encoder_layers: 1
  
  # 增强AIFI特定参数 - 针对道路裂缝检测优化
  use_enhanced_aifi: True          # 启用增强版AIFI
  aifi_type: 'enhanced'            # 使用多尺度+方向感知版本
  window_sizes: [4, 7, 14]         # 多尺度窗口：细节、标准、全局
  use_directional: True            # 启用方向感知机制
  use_adaptive_weights: True       # 启用自适应权重学习

# 训练参数 - 与原始deim_hgnetv2_s_svrdd.yml保持完全一致
optimizer:
  type: AdamW
  params: 
    - 
      params: '^(?=.*backbone)(?!.*bn).*'
      lr: 0.0005
    - 
      params: '^(?=.*(?:norm|bn)).*'
      weight_decay: 0.
  lr: 0.0005
  betas: [0.9, 0.999]
  weight_decay: 0.0001

train_dataloader:
  total_batch_size: 16
  collate_fn:
    mixup_epochs: [10, 160]
    stop_epoch: 160

val_dataloader:
  total_batch_size: 16
  num_workers: 2
  shuffle: False

lrsheduler: flatcosine
lr_gamma: 0.5
warmup_iter: 50

early_stopping:
  monitor: val_loss
  patience: 10
  mode: min
  min_delta: 0.0001
  verbose: True