task: detection

evaluator:
  type: CocoEvaluator
  iou_types: ['bbox', ]

num_classes: 20

train_dataloader:
  type: DataLoader
  dataset:
    type: VOCDetection
    root: ./dataset/voc/
    ann_file: trainval.txt
    label_file: label_list.txt
    transforms:
      type: Compose
      ops: ~
  shuffle: True
  num_workers: 4
  drop_last: True
  collate_fn:
    type: BatchImageCollateFunction


val_dataloader:
  type: DataLoader
  dataset:
    type: VOCDetection
    root: ./dataset/voc/
    ann_file: test.txt
    label_file: label_list.txt
    transforms:
      type: Compose
      ops: ~
  shuffle: False
  num_workers: 4
  drop_last: False
  collate_fn:
    type: BatchImageCollateFunction
