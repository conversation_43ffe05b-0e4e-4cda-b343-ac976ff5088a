__include__: [
  './dfine_hgnetv2_s_coco.yml',
  '../base/deim.yml',
  '../dataset/svrdd_detection.yml'
]
print_freq: 100
output_dir: /hy-tmp/outputs/deimtest

epoches: 150

# 添加HybridEncoder配置，控制FADE下采样的使用
#HybridEncoder:
  # 设置use_fade参数来控制是否使用FADE下采样
  #use_fade: True  # True: 使用SCDown进行下采样, False: 使用普通卷积

# 添加HGNetv2配置，控制PConv的使用
#HGNetv2:
  # 设置use_pconv参数来控制是否使用PConv
  # use_pconv: True  # True: 使用PConv(分组卷积)进行下采样, False: 使用普通卷积

optimizer:
  type: AdamW
  params: 
    - 
      params: '^(?=.*backbone)(?!.*bn).*$'
      lr: 0.0005
    - 
      params: '^(?=.*(?:norm|bn)).*$'     # except bias
      weight_decay: 0.
  lr: 0.0005
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# batch size
train_dataloader:
  total_batch_size: 16
  collate_fn:
    mixup_epochs: [10, 160]
    stop_epoch: 160

val_dataloader:
  total_batch_size: 16
  num_workers: 2
  shuffle: False

lrsheduler: flatcosine
lr_gamma: 0.5
warmup_iter: 50  # 前50个epoch进行warmup

# 早停配置
early_stopping:
  monitor: val_loss   # 监控的指标，可根据实际情况调整
  patience: 10       # 容忍多少个epoch无提升
  mode: min          # 监控指标的优化方向
  min_delta: 0.0001  # 最小变化量
  verbose: True      # 是否打印日志