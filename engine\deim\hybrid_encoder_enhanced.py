"""
DEIM: DETR with Improved Matching for Fast Convergence
Copyright (c) 2024 The DEIM Authors. All Rights Reserved.
---------------------------------------------------------------------------------
Enhanced HybridEncoder with Advanced AIFI (Advanced Improved Feature Integration)
Based on Multi-Scale Windowed Self-Attention and Directional Awareness for Road Crack Detection
"""

import copy
from collections import OrderedDict
import torch
import torch.nn as nn
import torch.nn.functional as F

# 完整继承父类的所有依赖
from .hybrid_encoder import (
    HybridEncoder, 
    TransformerEncoderLayer, 
    TransformerEncoder,
    ConvNormLayer_fuse,
    ConvNormLayer,
    SCDown,
    VGGBlock,
    CSPLayer,
    RepNCSPELAN4
)
from .enhanced_aifi import EnhancedAIFI, CrackAdaptiveAIFI
from ..core import register

__all__ = ['HybridEncoderEnhanced']


@register()
class HybridEncoderEnhanced(HybridEncoder):
    """
    增强版HybridEncoder，集成多尺度AIFI和方向感知机制
    专门针对道路裂缝检测任务优化
    继承自原始HybridEncoder，只替换encoder部分使用增强版AIFI
    """
    
    def __init__(
        self,
        # 基础参数 - 与父类完全兼容
        in_channels=[512, 1024, 2048],
        feat_strides=[8, 16, 32],
        hidden_dim=256,
        nhead=8,
        dim_feedforward=1024,
        dropout=0.0,
        enc_act='gelu',
        use_encoder_idx=[2],
        num_encoder_layers=1,
        pe_temperature=10000,
        expansion=1.0,
        depth_mult=1.0,
        act='silu',
        eval_spatial_size=None,
        version='dfine',
        # 增强AIFI特定参数
        use_enhanced_aifi=True,
        aifi_type='enhanced',  # 'enhanced' 或 'adaptive'
        window_sizes=[4, 7, 14],  # 多尺度窗口大小
        use_directional=True,     # 启用方向感知（针对裂缝）
        use_adaptive_weights=True, # 启用自适应权重
        adaptive_range=(4, 14),   # 自适应窗口范围
        # 兼容性参数
        use_windowed_attention=None,  # 兼容旧配置
        window_size=7,
        shift_size=0,
        **kwargs
    ):
        # 处理兼容性参数
        if use_windowed_attention is not None:
            use_enhanced_aifi = use_windowed_attention
        
        # 调用父类构造函数，只传递父类支持的基础参数
        super().__init__(
            in_channels=in_channels,
            feat_strides=feat_strides,
            hidden_dim=hidden_dim,
            nhead=nhead,
            dim_feedforward=dim_feedforward,
            dropout=dropout,
            enc_act=enc_act,
            use_encoder_idx=use_encoder_idx,
            num_encoder_layers=num_encoder_layers,
            pe_temperature=pe_temperature,
            expansion=expansion,
            depth_mult=depth_mult,
            act=act,
            eval_spatial_size=eval_spatial_size,
            version=version
        )
        
        # 确保这些属性在子类中也可用（防止父类没有保存）
        self.nhead = nhead
        self.dim_feedforward = dim_feedforward
        self.dropout = dropout
        self.enc_act = enc_act
        
        # 增强AIFI特定参数
        self.use_enhanced_aifi = use_enhanced_aifi
        self.aifi_type = aifi_type
        self.window_sizes = window_sizes
        self.use_directional = use_directional
        self.use_adaptive_weights = use_adaptive_weights
        self.adaptive_range = adaptive_range
        
        # 兼容性参数
        self.window_size = window_size
        self.shift_size = shift_size
        
        # 重新构建encoder以使用增强版AIFI
        if use_enhanced_aifi and num_encoder_layers > 0:
            self._build_enhanced_aifi_encoder()
    
    def _build_enhanced_aifi_encoder(self):
        """构建基于增强AIFI的编码器，替换原有的encoder"""
        
        if self.aifi_type == 'enhanced':
            # 使用增强版AIFI（多尺度 + 方向感知）
            aifi_encoder_layer = EnhancedAIFI(
                c=self.hidden_dim,
                nhead=self.nhead,
                window_sizes=self.window_sizes,
                use_directional=self.use_directional,
                use_adaptive_weights=self.use_adaptive_weights,
                dropout=self.dropout,
                act=self.enc_act
            )
        elif self.aifi_type == 'adaptive':
            # 使用自适应AIFI（动态窗口调整）
            aifi_encoder_layer = CrackAdaptiveAIFI(
                c=self.hidden_dim,
                nhead=self.nhead,
                base_window_size=self.window_size,
                adaptive_range=self.adaptive_range,
                dropout=self.dropout
            )
        else:
            raise ValueError(f"不支持的AIFI类型: {self.aifi_type}")
        
        # 重新构建encoder - 使用ModuleList包装以保持与父类一致的结构
        self.encoder = nn.ModuleList([
            nn.ModuleList([copy.deepcopy(aifi_encoder_layer) for _ in range(self.num_encoder_layers)]) 
            for _ in range(len(self.use_encoder_idx))
        ])
    
    def forward(self, feats):
        """
        前向传播，只修改encoder部分使用增强版AIFI，其他部分完全继承父类
        确保与原始HybridEncoder的输出完全兼容
        """
        assert len(feats) == len(self.in_channels), f"输入特征数量 {len(feats)} 与期望的通道数量 {len(self.in_channels)} 不匹配"
        
        # 通道投影 - 与父类完全相同
        proj_feats = [self.input_proj[i](feat) for i, feat in enumerate(feats)]

        # encoder with Enhanced AIFI
        if self.num_encoder_layers > 0 and self.use_enhanced_aifi:
            for i, enc_ind in enumerate(self.use_encoder_idx):
                h, w = proj_feats[enc_ind].shape[2:]
                
                # 验证通道匹配
                assert proj_feats[enc_ind].shape[1] == self.hidden_dim, \
                    f"特征通道数 {proj_feats[enc_ind].shape[1]} 与hidden_dim {self.hidden_dim} 不匹配"
                
                # flatten [B, C, H, W] to [B, HxW, C]
                src_flatten = proj_feats[enc_ind].flatten(2).permute(0, 2, 1)
                
                # 使用增强AIFI编码器处理特征
                memory = src_flatten
                for layer in self.encoder[i]:
                    # 增强AIFI接受序列输入和hw_shape
                    memory = layer(memory, hw_shape=(h, w))
                
                # 确保输出形状正确
                assert memory.shape == src_flatten.shape, \
                    f"AIFI输出形状 {memory.shape} 与输入形状 {src_flatten.shape} 不匹配"
                
                # 重塑回特征图格式
                proj_feats[enc_ind] = memory.permute(0, 2, 1).reshape(-1, self.hidden_dim, h, w).contiguous()
        
        elif self.num_encoder_layers > 0:
            # 回退到父类的encoder处理（标准Self-Attention）
            return super().forward(feats)

        # 其他部分完全继承父类的处理逻辑
        # broadcasting and fusion (FPN)
        inner_outs = [proj_feats[-1]]
        for idx in range(len(self.in_channels) - 1, 0, -1):
            feat_heigh = inner_outs[0]
            feat_low = proj_feats[idx - 1]
            feat_heigh = self.lateral_convs[len(self.in_channels) - 1 - idx](feat_heigh)
            inner_outs[0] = feat_heigh
            upsample_feat = F.interpolate(feat_heigh, scale_factor=2., mode='nearest')
            inner_out = self.fpn_blocks[len(self.in_channels)-1-idx](torch.cat([upsample_feat, feat_low], dim=1))
            inner_outs.insert(0, inner_out)

        # bottom-up fusion (PAN)
        outs = [inner_outs[0]]
        for idx in range(len(self.in_channels) - 1):
            feat_low = outs[-1]
            feat_height = inner_outs[idx + 1]
            downsample_feat = self.downsample_convs[idx](feat_low)
            out = self.pan_blocks[idx](torch.cat([downsample_feat, feat_height], dim=1))
            outs.append(out)

        # 验证输出形状与父类一致
        assert len(outs) == len(self.in_channels), "输出特征数量不匹配"
        for i, out in enumerate(outs):
            assert out.shape[1] == self.hidden_dim, f"输出 {i} 的通道数 {out.shape[1]} 不等于 hidden_dim {self.hidden_dim}"

        return outs
    
    def get_enhanced_aifi_info(self):
        """获取增强AIFI的配置信息，用于调试和监控"""
        return {
            'use_enhanced_aifi': self.use_enhanced_aifi,
            'aifi_type': self.aifi_type,
            'window_sizes': self.window_sizes,
            'use_directional': self.use_directional,
            'use_adaptive_weights': self.use_adaptive_weights,
            'adaptive_range': self.adaptive_range,
            'num_encoder_layers': self.num_encoder_layers,
            'use_encoder_idx': self.use_encoder_idx,
            'hidden_dim': self.hidden_dim,
            'nhead': self.nhead
        }
    
    def switch_aifi_type(self, aifi_type='enhanced'):
        """动态切换AIFI类型（用于实验和对比）"""
        if aifi_type != self.aifi_type:
            self.aifi_type = aifi_type
            if self.use_enhanced_aifi and self.num_encoder_layers > 0:
                self._build_enhanced_aifi_encoder()
                print(f"已切换到 {aifi_type} AIFI")
    
    def enable_enhanced_aifi(self, enable=True):
        """启用或禁用增强AIFI（用于消融实验）"""
        if enable != self.use_enhanced_aifi:
            self.use_enhanced_aifi = enable
            if enable and self.num_encoder_layers > 0:
                self._build_enhanced_aifi_encoder()
                print("已启用增强AIFI")
            else:
                # 重建为标准encoder
                encoder_layer = TransformerEncoderLayer(
                    self.hidden_dim,
                    nhead=self.nhead,
                    dim_feedforward=self.dim_feedforward,
                    dropout=self.dropout,
                    activation=self.enc_act
                )
                self.encoder = nn.ModuleList([
                    TransformerEncoder(copy.deepcopy(encoder_layer), self.num_encoder_layers) 
                    for _ in range(len(self.use_encoder_idx))
                ])
                print("已禁用增强AIFI，回退到标准Self-Attention")