"""
Efficient HGNetV2 with adaptive feature refinement and parameter optimization
While maintaining compatibility with original HGNetV2.

Copyright (c) 2024 The D-FINE Authors. All Rights Reserved.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import os
from .common import FrozenBatchNorm2d
from ..core import register
import logging
from .hgnetv2 import HGNetv2, ConvBNAct, StemBlock

class AdaptiveFeatureRefinement(nn.Module):
    """Efficient channel and spatial attention with minimal parameters"""
    def __init__(self, channels, reduction_ratio=8):
        super().__init__()
        reduced_channels = max(channels // reduction_ratio, 8)
        
        # Channel attention branch
        self.channel_gate = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, reduced_channels, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(reduced_channels, channels, 1, bias=False),
        )
        
        # Spatial attention branch
        self.spatial_gate = nn.Sequential(
            nn.Conv2d(channels, 1, kernel_size=7, padding=3, bias=False),
            nn.BatchNorm2d(1),
        )
        
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        # Channel attention
        channel_att = self.sigmoid(self.channel_gate(x))
        x = x * channel_att
        # Spatial attention
        spatial_att = self.sigmoid(self.spatial_gate(x))
        return x * spatial_att

class EfficientBlock(nn.Module):
    """Efficient HG Block with adaptive feature refinement"""
    def __init__(
            self,
            in_chs,
            mid_chs,
            out_chs,
            layer_num,
            kernel_size=3,
            residual=False,
            light_block=False,
            use_lab=False,
            drop_path=0.,
    ):
        super().__init__()
        self.residual = residual
        self.layers = nn.ModuleList()
        
        # Optimize channel dimensions
        scale_factor = 0.75 if light_block else 1.0
        actual_mid_chs = int(mid_chs * scale_factor)
        
        for i in range(layer_num):
            if light_block:
                self.layers.append(
                    nn.Sequential(
                        # Depthwise conv
                        nn.Conv2d(
                            in_chs if i == 0 else actual_mid_chs,
                            in_chs if i == 0 else actual_mid_chs,
                            kernel_size=kernel_size,
                            padding=kernel_size//2,
                            groups=in_chs if i == 0 else actual_mid_chs,
                            bias=False
                        ),
                        nn.BatchNorm2d(in_chs if i == 0 else actual_mid_chs),
                        nn.ReLU(inplace=True),
                        # Pointwise conv
                        nn.Conv2d(
                            in_chs if i == 0 else actual_mid_chs,
                            actual_mid_chs,
                            1,
                            bias=False
                        ),
                        nn.BatchNorm2d(actual_mid_chs),
                        nn.ReLU(inplace=True)
                    )
                )
            else:
                self.layers.append(
                    ConvBNAct(
                        in_chs if i == 0 else mid_chs,
                        mid_chs,
                        kernel_size=kernel_size,
                        stride=1,
                        use_lab=use_lab,
                    )
                )

        # Feature aggregation
        total_chs = in_chs + layer_num * (actual_mid_chs if light_block else mid_chs)
        
        self.aggregation = nn.Sequential(
            nn.Conv2d(total_chs, out_chs, 1, bias=False),
            nn.BatchNorm2d(out_chs),
            nn.ReLU(inplace=True),
            AdaptiveFeatureRefinement(out_chs)
        )

        self.drop_path = nn.Dropout(drop_path) if drop_path > 0 else nn.Identity()

    def forward(self, x):
        identity = x
        outputs = [x]
        for layer in self.layers:
            x = layer(x)
            outputs.append(x)
        x = torch.cat(outputs, dim=1)
        x = self.aggregation(x)
        if self.residual:
            x = self.drop_path(x) + identity
        return x

class EfficientStage(nn.Module):
    """Efficient HG Stage with improved feature propagation"""
    def __init__(
            self,
            in_chs,
            mid_chs,
            out_chs,
            block_num,
            layer_num,
            downsample=True,
            light_block=False,
            kernel_size=3,
            use_lab=False,
            drop_path=0.,
    ):
        super().__init__()
        
        # Efficient downsampling
        if downsample:
            self.downsample = nn.Sequential(
                nn.Conv2d(in_chs, in_chs, kernel_size=3, stride=2, 
                         padding=1, groups=in_chs, bias=False),
                nn.BatchNorm2d(in_chs),
                nn.Conv2d(in_chs, in_chs, kernel_size=1, bias=False),
                nn.BatchNorm2d(in_chs)
            )
        else:
            self.downsample = nn.Identity()

        # Progressive feature refinement
        blocks_list = []
        for i in range(block_num):
            blocks_list.append(
                EfficientBlock(
                    in_chs if i == 0 else out_chs,
                    mid_chs,
                    out_chs,
                    layer_num,
                    residual=False if i == 0 else True,
                    kernel_size=kernel_size,
                    light_block=light_block,
                    use_lab=use_lab,
                    drop_path=drop_path[i] if isinstance(drop_path, (list, tuple)) else drop_path,
                )
            )
        self.blocks = nn.Sequential(*blocks_list)

    def forward(self, x):
        x = self.downsample(x)
        x = self.blocks(x)
        return x

@register()
class HGNetv2Efficient(HGNetv2):
    """
    Efficient HGNetV2 with adaptive feature refinement and parameter optimization
    while maintaining compatibility with original HGNetv2.
    """
    def __init__(self,
                 name,
                 use_lab=False,
                 return_idx=[1, 2, 3],
                 freeze_stem_only=True,
                 freeze_at=0,
                 freeze_norm=True,
                 pretrained=True,
                 local_model_dir='weight/hgnetv2/'):
        # Initialize parent class without stages
        nn.Module.__init__(self)
        self.use_lab = use_lab
        self.return_idx = return_idx
        
        stem_channels = self.arch_configs[name]['stem_channels']
        stage_config = self.arch_configs[name]['stage_config']
        self.download_url = self.arch_configs[name]['url']

        self._out_strides = [4, 8, 16, 32]
        self._out_channels = [stage_config[k][2] for k in stage_config]

        # Use original stem
        self.stem = StemBlock(
            in_chs=stem_channels[0],
            mid_chs=stem_channels[1],
            out_chs=stem_channels[2],
            use_lab=use_lab)

        # Efficient stages
        self.stages = nn.ModuleList()
        for i, k in enumerate(stage_config):
            in_channels, mid_channels, out_channels, block_num, downsample, light_block, kernel_size, layer_num = stage_config[k]
            
            self.stages.append(
                EfficientStage(
                    in_channels,
                    mid_channels,
                    out_channels,
                    block_num,
                    layer_num,
                    downsample,
                    light_block,
                    kernel_size,
                    use_lab,
                    drop_path=0.))

        # Apply freezing if specified
        if freeze_at >= 0:
            self._freeze_parameters(self.stem)
            if not freeze_stem_only:
                for i in range(min(freeze_at + 1, len(self.stages))):
                    self._freeze_parameters(self.stages[i])

        if freeze_norm:
            self._freeze_norm(self)

        # Load pretrained weights if specified
        if pretrained:
            self._load_pretrained(name, local_model_dir)

    def _load_pretrained(self, name, local_model_dir):
        """Helper method to load pretrained weights with proper error handling"""
        RED, GREEN, RESET = "\033[91m", "\033[92m", "\033[0m"
        try:
            model_path = os.path.join(local_model_dir, f'PPHGNetV2_{name}_stage1.pth')
            if os.path.exists(model_path):
                state = torch.load(model_path, map_location='cpu')
                print(f"Loaded stage1 {name} HGNetV2 from local file.")
            else:
                if torch.distributed.get_rank() == 0:
                    print(GREEN + "Downloading pretrained HGNetV2..." + RESET)
                    state = torch.hub.load_state_dict_from_url(
                        self.download_url, 
                        map_location='cpu',
                        model_dir=local_model_dir
                    )
                    torch.distributed.barrier()
                else:
                    torch.distributed.barrier()
                    state = torch.load(model_path)
                print(f"Loaded stage1 {name} HGNetV2 from URL.")

            # Load compatible parameters
            model_dict = self.state_dict()
            pretrained_dict = {k: v for k, v in state.items() 
                             if k in model_dict and v.shape == model_dict[k].shape}
            model_dict.update(pretrained_dict)
            self.load_state_dict(model_dict)
            
            if len(pretrained_dict) < len(model_dict):
                print(f"Note: {len(model_dict) - len(pretrained_dict)} parameters were randomly initialized")

        except Exception as e:
            if torch.distributed.get_rank() == 0:
                logging.error(RED + f"Failed to load pretrained model: {str(e)}" + RESET)
                logging.error(GREEN + f"Please download manually from {self.download_url}" + RESET)
            raise e
