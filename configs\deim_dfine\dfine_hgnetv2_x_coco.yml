__include__: [
  '../dataset/coco_detection.yml',
  '../runtime.yml',
  '../base/dataloader.yml',
  '../base/optimizer.yml',
  '../base/dfine_hgnetv2.yml',
]

output_dir: ./output/dfine_hgnetv2_x_coco


DEIM:
  backbone: HGNetv2

HGNetv2:
  name: 'B5'
  return_idx: [1, 2, 3]
  freeze_stem_only: True
  freeze_at: 0
  freeze_norm: True

HybridEncoder:
  # intra
  hidden_dim: 384
  dim_feedforward: 2048

DFINETransformer:
  feat_channels: [384, 384, 384]
  reg_scale: 8

optimizer:
  type: AdamW
  params:
    -
      params: '^(?=.*backbone)(?!.*norm|bn).*$'
      lr: 0.0000025
    -
      params: '^(?=.*(?:encoder|decoder))(?=.*(?:norm|bn)).*$'
      weight_decay: 0.

  lr: 0.00025
  betas: [0.9, 0.999]
  weight_decay: 0.000125


# Increase to search for the optimal ema
epoches: 80 # 72 + 2n
train_dataloader:
  dataset:
    transforms:
      policy:
        epoch: 72
  collate_fn:
    stop_epoch: 72
    ema_restart_decay: 0.9998
    base_size_repeat: 3
