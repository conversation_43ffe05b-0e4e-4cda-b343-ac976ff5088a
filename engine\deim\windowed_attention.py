"""
Windowed Self-Attention Implementation for DEIM
Copyright (c) 2024 The DEIM Authors. All Rights Reserved.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Optional, Tuple


def window_partition(x: torch.Tensor, window_size: int) -> torch.Tensor:
    """
    将特征图分割成窗口，支持不能整除的情况
    Args:
        x: (B, H, W, C)
        window_size: 窗口大小
    Returns:
        windows: (num_windows*B, window_size, window_size, C)
    """
    B, H, W, C = x.shape
    
    # 如果尺寸不能被窗口大小整除，进行padding
    pad_h = (window_size - H % window_size) % window_size
    pad_w = (window_size - W % window_size) % window_size
    
    if pad_h > 0 or pad_w > 0:
        x = F.pad(x, (0, 0, 0, pad_w, 0, pad_h))
        H, W = H + pad_h, W + pad_w
    
    x = x.view(B, H // window_size, window_size, W // window_size, window_size, C)
    windows = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(-1, window_size, window_size, C)
    return windows


def window_reverse(windows: torch.Tensor, window_size: int, H: int, W: int) -> torch.Tensor:
    """
    将窗口合并回特征图，支持padding的情况
    Args:
        windows: (num_windows*B, window_size, window_size, C)
        window_size: 窗口大小
        H: 原始特征图高度
        W: 原始特征图宽度
    Returns:
        x: (B, H, W, C)
    """
    # 计算padding后的尺寸
    pad_h = (window_size - H % window_size) % window_size
    pad_w = (window_size - W % window_size) % window_size
    H_pad, W_pad = H + pad_h, W + pad_w
    
    B = int(windows.shape[0] / (H_pad * W_pad / window_size / window_size))
    x = windows.view(B, H_pad // window_size, W_pad // window_size, window_size, window_size, -1)
    x = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(B, H_pad, W_pad, -1)
    
    # 如果有padding，需要裁剪回原始尺寸
    if pad_h > 0 or pad_w > 0:
        x = x[:, :H, :W, :]
    
    return x


class WindowedMultiHeadAttention(nn.Module):
    """
    Windowed Multi-Head Self-Attention模块
    支持hw_shape输入，用于替换标准的MultiheadAttention
    改进版本：支持动态窗口大小和更好的通道对齐
    """
    
    def __init__(
        self,
        embed_dim: int,
        num_heads: int,
        window_size: int = 7,
        shift_size: int = 0,
        dropout: float = 0.0,
        bias: bool = True,
        batch_first: bool = True,
        qkv_bias: bool = True
    ):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.window_size = window_size
        self.shift_size = shift_size
        self.dropout = dropout
        self.batch_first = batch_first
        
        assert embed_dim % num_heads == 0, f"embed_dim {embed_dim} must be divisible by num_heads {num_heads}"
        self.head_dim = embed_dim // num_heads
        self.scale = self.head_dim ** -0.5
        
        # 线性投影层 - 改进：支持独立的q,k,v投影以更好匹配原始MultiheadAttention
        self.q_proj = nn.Linear(embed_dim, embed_dim, bias=qkv_bias)
        self.k_proj = nn.Linear(embed_dim, embed_dim, bias=qkv_bias)
        self.v_proj = nn.Linear(embed_dim, embed_dim, bias=qkv_bias)
        self.out_proj = nn.Linear(embed_dim, embed_dim, bias=bias)
        self.attn_drop = nn.Dropout(dropout)
        self.proj_drop = nn.Dropout(dropout)
        
        # 相对位置偏置表 - 支持动态窗口大小
        self.relative_position_bias_table = nn.Parameter(
            torch.zeros((2 * window_size - 1) * (2 * window_size - 1), num_heads)
        )
        
        # 预计算相对位置索引
        self._build_relative_position_index(window_size)
        
        # 初始化
        self._reset_parameters()
    
    def _build_relative_position_index(self, window_size):
        """构建相对位置索引"""
        coords_h = torch.arange(window_size)
        coords_w = torch.arange(window_size)
        coords = torch.stack(torch.meshgrid([coords_h, coords_w], indexing='ij'))
        coords_flatten = torch.flatten(coords, 1)
        relative_coords = coords_flatten[:, :, None] - coords_flatten[:, None, :]
        relative_coords = relative_coords.permute(1, 2, 0).contiguous()
        relative_coords[:, :, 0] += window_size - 1
        relative_coords[:, :, 1] += window_size - 1
        relative_coords[:, :, 0] *= 2 * window_size - 1
        relative_position_index = relative_coords.sum(-1)
        self.register_buffer("relative_position_index", relative_position_index)
    
    def _reset_parameters(self):
        """初始化参数"""
        nn.init.trunc_normal_(self.relative_position_bias_table, std=0.02)
        # 使用Xavier初始化投影层
        nn.init.xavier_uniform_(self.q_proj.weight)
        nn.init.xavier_uniform_(self.k_proj.weight)
        nn.init.xavier_uniform_(self.v_proj.weight)
        nn.init.xavier_uniform_(self.out_proj.weight)
        if self.q_proj.bias is not None:
            nn.init.constant_(self.q_proj.bias, 0)
            nn.init.constant_(self.k_proj.bias, 0)
            nn.init.constant_(self.v_proj.bias, 0)
        if self.out_proj.bias is not None:
            nn.init.constant_(self.out_proj.bias, 0)
        
    def forward(
        self, 
        query: torch.Tensor, 
        key: torch.Tensor, 
        value: torch.Tensor,
        key_padding_mask: Optional[torch.Tensor] = None,
        need_weights: bool = True,
        attn_mask: Optional[torch.Tensor] = None,
        hw_shape: Optional[Tuple[int, int]] = None
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        Args:
            query: (B, L, C) if batch_first else (L, B, C)
            key: (B, L, C) if batch_first else (L, B, C)  
            value: (B, L, C) if batch_first else (L, B, C)
            hw_shape: (H, W) 特征图的高度和宽度
        """
        
        # 确保输入格式为 batch_first
        if not self.batch_first:
            query = query.transpose(0, 1)
            key = key.transpose(0, 1)
            value = value.transpose(0, 1)
            
        B, L, C = query.shape
        
        # 如果没有提供hw_shape，尝试推断为正方形
        if hw_shape is None:
            H = W = int(math.sqrt(L))
            assert H * W == L, f"序列长度 {L} 不是完全平方数，需要提供 hw_shape"
        else:
            H, W = hw_shape
            assert H * W == L, f"hw_shape {hw_shape} 与序列长度 {L} 不匹配"
        
        # 动态调整窗口大小以适应特征图尺寸
        effective_window_size = min(self.window_size, H, W)
        effective_shift_size = min(self.shift_size, effective_window_size // 2) if self.shift_size > 0 else 0
        
        # 如果特征图太小，直接使用全局注意力
        if H <= effective_window_size and W <= effective_window_size:
            return self._global_attention(query, key, value, need_weights)
        
        # 将序列重塑为特征图格式 (B, H, W, C)
        x = query.view(B, H, W, C)
        
        # 如果需要，进行循环移位
        if effective_shift_size > 0:
            shifted_x = torch.roll(x, shifts=(-effective_shift_size, -effective_shift_size), dims=(1, 2))
        else:
            shifted_x = x
            
        # 分割成窗口
        x_windows = window_partition(shifted_x, effective_window_size)  # (nW*B, window_size, window_size, C)
        x_windows = x_windows.view(-1, effective_window_size * effective_window_size, C)  # (nW*B, window_size*window_size, C)
        
        # 多头注意力计算 - 使用独立的q,k,v投影
        B_win = x_windows.shape[0]
        N = effective_window_size * effective_window_size
        
        # 分别计算q, k, v
        q = self.q_proj(x_windows).reshape(B_win, N, self.num_heads, self.head_dim).permute(0, 2, 1, 3)  # (B_win, num_heads, N, head_dim)
        k = self.k_proj(x_windows).reshape(B_win, N, self.num_heads, self.head_dim).permute(0, 2, 1, 3)  # (B_win, num_heads, N, head_dim)
        v = self.v_proj(x_windows).reshape(B_win, N, self.num_heads, self.head_dim).permute(0, 2, 1, 3)  # (B_win, num_heads, N, head_dim)
        
        q = q * self.scale
        attn = (q @ k.transpose(-2, -1))
        
        # 添加相对位置偏置（只有当窗口大小匹配时才使用预计算的偏置）
        if effective_window_size == self.window_size:
            relative_position_bias = self.relative_position_bias_table[self.relative_position_index.view(-1)].view(
                self.window_size * self.window_size, self.window_size * self.window_size, -1
            )
            relative_position_bias = relative_position_bias.permute(2, 0, 1).contiguous()
            attn = attn + relative_position_bias.unsqueeze(0)
        else:
            # 动态计算相对位置偏置
            relative_position_bias = self._get_relative_position_bias(effective_window_size, attn.device)
            attn = attn + relative_position_bias.unsqueeze(0)
        
        # 如果有移位，创建注意力掩码
        if effective_shift_size > 0:
            attn_mask = self._create_shift_mask(H, W, effective_window_size, effective_shift_size, x.device)
            # 确保掩码维度与注意力张量匹配
            # attn: (B_win, num_heads, N, N), attn_mask: (num_windows, N, N)
            # 需要将掩码扩展到所有批次和头
            num_windows_per_batch = attn_mask.shape[0]
            batch_size = B_win // num_windows_per_batch

            # 重复掩码以匹配批次大小
            attn_mask = attn_mask.repeat(batch_size, 1, 1)  # (B_win, N, N)
            attn = attn + attn_mask.unsqueeze(1)  # 广播到所有头
        
        attn = F.softmax(attn, dim=-1)
        attn = self.attn_drop(attn)
        
        x = (attn @ v).transpose(1, 2).reshape(B_win, N, C)
        
        # 合并窗口
        x = x.view(-1, effective_window_size, effective_window_size, C)
        shifted_x = window_reverse(x, effective_window_size, H, W)
        
        # 反向循环移位
        if effective_shift_size > 0:
            x = torch.roll(shifted_x, shifts=(effective_shift_size, effective_shift_size), dims=(1, 2))
        else:
            x = shifted_x
            
        # 重塑回序列格式
        x = x.view(B, H * W, C)
        
        # 输出投影
        x = self.out_proj(x)
        x = self.proj_drop(x)
        
        # 如果原始输入不是batch_first，转换回去
        if not self.batch_first:
            x = x.transpose(0, 1)
            
        # 返回注意力权重（如果需要）
        attn_weights = attn.mean(dim=1) if need_weights else None
        
        return x, attn_weights
    
    def _global_attention(self, query, key, value, need_weights):
        """当特征图太小时使用全局注意力"""
        B, L, C = query.shape
        
        q = self.q_proj(query).reshape(B, L, self.num_heads, self.head_dim).permute(0, 2, 1, 3)
        k = self.k_proj(key).reshape(B, L, self.num_heads, self.head_dim).permute(0, 2, 1, 3)
        v = self.v_proj(value).reshape(B, L, self.num_heads, self.head_dim).permute(0, 2, 1, 3)
        
        q = q * self.scale
        attn = (q @ k.transpose(-2, -1))
        attn = F.softmax(attn, dim=-1)
        attn = self.attn_drop(attn)
        
        x = (attn @ v).transpose(1, 2).reshape(B, L, C)
        x = self.out_proj(x)
        x = self.proj_drop(x)
        
        attn_weights = attn.mean(dim=1) if need_weights else None
        return x, attn_weights
    
    def _get_relative_position_bias(self, window_size, device):
        """动态计算相对位置偏置"""
        coords_h = torch.arange(window_size, device=device)
        coords_w = torch.arange(window_size, device=device)
        coords = torch.stack(torch.meshgrid([coords_h, coords_w], indexing='ij'))
        coords_flatten = torch.flatten(coords, 1)
        relative_coords = coords_flatten[:, :, None] - coords_flatten[:, None, :]
        relative_coords = relative_coords.permute(1, 2, 0).contiguous()
        relative_coords[:, :, 0] += window_size - 1
        relative_coords[:, :, 1] += window_size - 1
        relative_coords[:, :, 0] *= 2 * window_size - 1
        relative_position_index = relative_coords.sum(-1)
        
        # 使用插值来适应不同的窗口大小
        if window_size != self.window_size:
            # 简化处理：使用零偏置
            return torch.zeros(window_size * window_size, window_size * window_size, self.num_heads, device=device).permute(2, 0, 1)
        else:
            return self.relative_position_bias_table[relative_position_index.view(-1)].view(
                window_size * window_size, window_size * window_size, -1
            ).permute(2, 0, 1).contiguous()
    
    def _create_shift_mask(self, H, W, window_size, shift_size, device):
        """创建移位注意力掩码"""
        # 计算padding后的尺寸，与window_partition保持一致
        pad_h = (window_size - H % window_size) % window_size
        pad_w = (window_size - W % window_size) % window_size
        H_pad, W_pad = H + pad_h, W + pad_w

        img_mask = torch.zeros((1, H_pad, W_pad, 1), device=device)
        h_slices = (slice(0, -window_size),
                   slice(-window_size, -shift_size),
                   slice(-shift_size, None))
        w_slices = (slice(0, -window_size),
                   slice(-window_size, -shift_size),
                   slice(-shift_size, None))
        cnt = 0
        for h in h_slices:
            for w in w_slices:
                img_mask[:, h, w, :] = cnt
                cnt += 1

        # 使用与window_partition完全一致的逻辑
        B, H_m, W_m, C = img_mask.shape
        img_mask = img_mask.view(B, H_m // window_size, window_size, W_m // window_size, window_size, C)
        mask_windows = img_mask.permute(0, 1, 3, 2, 4, 5).contiguous().view(-1, window_size, window_size, C)
        mask_windows = mask_windows.view(-1, window_size * window_size)

        # 计算实际的窗口数量，确保与注意力张量匹配
        num_windows = (H_pad // window_size) * (W_pad // window_size)

        # 只返回实际需要的窗口数量的掩码
        mask_windows = mask_windows[:num_windows]

        attn_mask = mask_windows.unsqueeze(1) - mask_windows.unsqueeze(2)
        attn_mask = attn_mask.masked_fill(attn_mask != 0, float(-100.0)).masked_fill(attn_mask == 0, float(0.0))
        return attn_mask


class WindowedTransformerEncoderLayer(nn.Module):
    """
    使用Windowed Self-Attention的Transformer编码器层
    """
    
    def __init__(
        self,
        d_model: int,
        nhead: int,
        dim_feedforward: int = 2048,
        dropout: float = 0.1,
        activation: str = "relu",
        normalize_before: bool = False,
        window_size: int = 7,
        shift_size: int = 0
    ):
        super().__init__()
        
        self.normalize_before = normalize_before
        self.window_size = window_size
        self.shift_size = shift_size
        
        # Windowed Self-Attention
        self.self_attn = WindowedMultiHeadAttention(
            embed_dim=d_model,
            num_heads=nhead,
            window_size=window_size,
            shift_size=shift_size,
            dropout=dropout,
            batch_first=True
        )
        
        # Feed Forward Network
        self.linear1 = nn.Linear(d_model, dim_feedforward)
        self.dropout = nn.Dropout(dropout)
        self.linear2 = nn.Linear(dim_feedforward, d_model)
        
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.dropout1 = nn.Dropout(dropout)
        self.dropout2 = nn.Dropout(dropout)
        
        # 激活函数
        if activation == "relu":
            self.activation = F.relu
        elif activation == "gelu":
            self.activation = F.gelu
        else:
            raise ValueError(f"不支持的激活函数: {activation}")
    
    @staticmethod
    def with_pos_embed(tensor, pos_embed):
        return tensor if pos_embed is None else tensor + pos_embed
    
    def forward(
        self, 
        src: torch.Tensor, 
        src_mask: Optional[torch.Tensor] = None, 
        pos_embed: Optional[torch.Tensor] = None,
        hw_shape: Optional[Tuple[int, int]] = None
    ) -> torch.Tensor:
        """
        Args:
            src: (B, L, C)
            hw_shape: (H, W) 特征图的高度和宽度
        """
        
        residual = src
        if self.normalize_before:
            src = self.norm1(src)
            
        # Self-attention with position embedding
        q = k = self.with_pos_embed(src, pos_embed)
        src2, _ = self.self_attn(q, k, src, attn_mask=src_mask, hw_shape=hw_shape)
        
        src = residual + self.dropout1(src2)
        if not self.normalize_before:
            src = self.norm1(src)
        
        # Feed forward
        residual = src
        if self.normalize_before:
            src = self.norm2(src)
            
        src2 = self.linear2(self.dropout(self.activation(self.linear1(src))))
        src = residual + self.dropout2(src2)
        
        if not self.normalize_before:
            src = self.norm2(src)
            
        return src


class AIFI(nn.Module):
    """
    Advanced Improved Feature Integration (AIFI) 模块
    基于Windowed Self-Attention的改进版本，支持多尺度特征融合
    """
    
    def __init__(
        self,
        c: int,  # 输入通道数
        nhead: int = 8,
        window_size: int = 7,
        shift_size: int = 0,
        dropout: float = 0.0,
        act: str = "gelu",
        normalize_before: bool = False,
        use_checkpoint: bool = False
    ):
        super().__init__()
        
        self.c = c
        self.nhead = nhead
        self.window_size = window_size
        self.shift_size = shift_size
        self.use_checkpoint = use_checkpoint
        
        # 通道对齐层
        self.channel_align = nn.Sequential(
            nn.Conv2d(c, c, 1, bias=False),
            nn.BatchNorm2d(c),
            nn.SiLU() if act == "silu" else nn.GELU()
        )
        
        # Windowed Self-Attention层
        self.attn_layer = WindowedTransformerEncoderLayer(
            d_model=c,
            nhead=nhead,
            dim_feedforward=c * 4,
            dropout=dropout,
            activation=act,
            normalize_before=normalize_before,
            window_size=window_size,
            shift_size=shift_size
        )
        
        # 输出投影
        self.out_proj = nn.Sequential(
            nn.Conv2d(c, c, 1, bias=False),
            nn.BatchNorm2d(c)
        )
        
        # 残差连接的权重
        self.gamma = nn.Parameter(torch.ones(1))
        
    def forward(self, x, hw_shape=None):
        """
        Args:
            x: 输入特征 (B, C, H, W) 或 (B, L, C)
            hw_shape: 当输入为序列格式时的空间尺寸 (H, W)
        Returns:
            输出特征，格式与输入相同
        """
        input_is_2d = len(x.shape) == 4
        
        if input_is_2d:
            # 输入是 (B, C, H, W) 格式
            B, C, H, W = x.shape
            hw_shape = (H, W)
            
            # 通道对齐
            x_aligned = self.channel_align(x)
            
            # 转换为序列格式 (B, L, C)
            x_seq = x_aligned.flatten(2).transpose(1, 2)  # (B, H*W, C)
            
        else:
            # 输入是 (B, L, C) 格式
            B, L, C = x.shape
            x_seq = x
            
            if hw_shape is None:
                # 尝试推断为正方形
                H = W = int(math.sqrt(L))
                assert H * W == L, f"序列长度 {L} 不是完全平方数，需要提供 hw_shape"
                hw_shape = (H, W)
            else:
                H, W = hw_shape
                assert H * W == L, f"hw_shape {hw_shape} 与序列长度 {L} 不匹配"
        
        # 应用Windowed Self-Attention
        if self.use_checkpoint:
            attn_out = torch.utils.checkpoint.checkpoint(
                self.attn_layer, x_seq, None, None, hw_shape
            )
        else:
            attn_out = self.attn_layer(x_seq, hw_shape=hw_shape)
        
        if input_is_2d:
            # 转换回2D格式
            H, W = hw_shape
            attn_out = attn_out.transpose(1, 2).view(B, C, H, W)
            
            # 输出投影
            out = self.out_proj(attn_out)
            
            # 残差连接
            return x + self.gamma * out
        else:
            # 保持序列格式
            return x_seq + self.gamma * attn_out


class MultiScaleAIFI(nn.Module):
    """
    多尺度AIFI模块，支持不同尺度的特征融合
    """
    
    def __init__(
        self,
        c: int,
        nhead: int = 8,
        window_sizes: list = [7, 14],
        dropout: float = 0.0,
        act: str = "gelu"
    ):
        super().__init__()
        
        self.c = c
        self.window_sizes = window_sizes
        
        # 多个不同窗口大小的AIFI模块
        self.aifi_modules = nn.ModuleList([
            AIFI(
                c=c,
                nhead=nhead,
                window_size=ws,
                shift_size=ws // 2,
                dropout=dropout,
                act=act
            ) for ws in window_sizes
        ])
        
        # 特征融合
        self.fusion = nn.Sequential(
            nn.Conv2d(c * len(window_sizes), c, 1, bias=False),
            nn.BatchNorm2d(c),
            nn.SiLU() if act == "silu" else nn.GELU()
        )
        
        # 注意力权重
        self.attention_weights = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(c, len(window_sizes), 1),
            nn.Softmax(dim=1)
        )
        
    def forward(self, x, hw_shape=None):
        """
        Args:
            x: 输入特征 (B, C, H, W) 或 (B, L, C)
            hw_shape: 当输入为序列格式时的空间尺寸 (H, W)
        """
        # 获取多尺度特征
        multi_scale_features = []
        for aifi in self.aifi_modules:
            feat = aifi(x, hw_shape)
            multi_scale_features.append(feat)
        
        if len(x.shape) == 4:  # 2D输入
            # 拼接多尺度特征
            concat_feat = torch.cat(multi_scale_features, dim=1)
            
            # 特征融合
            fused_feat = self.fusion(concat_feat)
            
            # 计算注意力权重
            attn_weights = self.attention_weights(x)  # (B, num_scales, 1, 1)
            
            # 加权融合
            weighted_features = []
            for i, feat in enumerate(multi_scale_features):
                weighted_features.append(feat * attn_weights[:, i:i+1])
            
            # 最终输出
            output = sum(weighted_features)
            return output + fused_feat
        
        else:  # 序列输入
            # 简单平均融合
            return sum(multi_scale_features) / len(multi_scale_features)