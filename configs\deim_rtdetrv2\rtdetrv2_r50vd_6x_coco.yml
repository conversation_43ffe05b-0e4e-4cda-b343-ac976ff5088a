__include__: [
  '../dataset/coco_detection.yml',
  '../runtime.yml',
  '../base/dataloader.yml',
  '../base/rt_optimizer.yml',
  '../base/rtdetrv2_r50vd.yml',
]


output_dir: ./outputs/rtdetrv2_r50vd_6x_coco


optimizer:
  type: AdamW
  params: 
    - 
      params: '^(?=.*backbone)(?!.*norm).*$'
      lr: 0.00001
    - 
      params: '^(?=.*(?:encoder|decoder))(?=.*(?:norm|bn)).*$'
      weight_decay: 0.

  lr: 0.0001
  betas: [0.9, 0.999]
  weight_decay: 0.0001