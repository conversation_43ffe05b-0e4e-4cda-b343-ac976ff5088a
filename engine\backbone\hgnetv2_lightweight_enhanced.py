"""
Lightweight Enhanced HGNetV2 for Crack Detection
轻量级增强版HGNetV2，严格控制参数量增长
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from .hgnetv2 import HGNetv2, HG_Stage, HG_Block, ConvBNAct, StemBlock, EseModule
from ..core import register

__all__ = ['HGNetv2LightweightEnhanced']
class LightweightCrackEnhancedHGBlock(HG_Block):
    """
    轻量级裂缝增强版HG_Block
    参数增长控制在20%以内
    """
    def __init__(self, in_chs, mid_chs, out_chs, layer_num, **kwargs):
        # 完全继承父类初始化
        super().__init__(in_chs, mid_chs, out_chs, layer_num, **kwargs)
        
        # ===== 轻量级增强模块设计 =====
        
        # 1. 轻量级方向感知 - 使用深度可分离卷积
        # 只使用out_chs//8的通道数，大幅减少参数
        enhance_chs = max(out_chs // 8, 16)  # 最少16通道，避免过小
        
        self.lightweight_direction = nn.Sequential(
            # 深度可分离卷积：先深度卷积，再点卷积
            nn.Conv2d(out_chs, out_chs, 3, padding=1, groups=out_chs, bias=False),  # 深度卷积
            nn.BatchNorm2d(out_chs),
            nn.Conv2d(out_chs, enhance_chs, 1, bias=False),  # 点卷积降维
            nn.BatchNorm2d(enhance_chs),
            nn.ReLU(inplace=True)
        )
        
        # 2. 轻量级细节增强 - 使用1x1卷积
        self.lightweight_detail = nn.Sequential(
            nn.Conv2d(out_chs, enhance_chs, 1, bias=False),
            nn.BatchNorm2d(enhance_chs),
            nn.ReLU(inplace=True)
        )
        
        # 3. 简化的对比度自适应 - 只使用全局平均池化
        self.contrast_gate = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(out_chs, 1, 1),  # 只输出1个通道
            nn.Sigmoid()
        )
        
        # 4. 轻量级特征融合 - 直接相加而不是拼接
        # 将增强特征映射回原始通道数
        self.direction_proj = nn.Conv2d(enhance_chs, out_chs, 1, bias=False)
        self.detail_proj = nn.Conv2d(enhance_chs, out_chs, 1, bias=False)
        
        # 5. 可学习的融合权重 - 只有3个参数
        self.direction_weight = nn.Parameter(torch.tensor(0.1))
        self.detail_weight = nn.Parameter(torch.tensor(0.1))
        self.contrast_weight = nn.Parameter(torch.tensor(0.2))
    
    def forward(self, x):
        # ===== 步骤1: 获得原始HG_Block输出 =====
        original_output = super().forward(x)
        
        # ===== 步骤2: 轻量级方向感知 =====
        direction_feat = self.lightweight_direction(original_output)
        direction_enhanced = self.direction_proj(direction_feat)
        
        # ===== 步骤3: 轻量级细节增强 =====
        detail_feat = self.lightweight_detail(original_output)
        detail_enhanced = self.detail_proj(detail_feat)
        
        # ===== 步骤4: 对比度门控 =====
        contrast_gate = self.contrast_gate(original_output)
        
        # ===== 步骤5: 轻量级特征融合（相加而非拼接）=====
        enhanced_output = (
            original_output + 
            self.direction_weight * direction_enhanced + 
            self.detail_weight * detail_enhanced * contrast_gate +
            self.contrast_weight * original_output * contrast_gate
        )
        
        return enhanced_output


class LightweightCrackEnhancedHGStage(HG_Stage):
    """
    轻量级裂缝增强版HG_Stage
    只在关键stage使用增强，进一步控制参数量
    """
    def __init__(self, in_chs, mid_chs, out_chs, block_num, layer_num, 
                 downsample=True, light_block=False, kernel_size=3, 
                 use_lab=False, agg='se', drop_path=0., 
                 use_enhancement=True):  # 新增：是否使用增强
        
        # 先初始化父类的downsample部分
        super().__init__(in_chs, mid_chs, out_chs, block_num, layer_num,
                        downsample, light_block, kernel_size, use_lab, agg, drop_path)
        
        # 重新构建blocks，选择性使用增强
        blocks_list = []
        for i in range(block_num):
            # 只在最后一个block使用增强，进一步减少参数
            use_block_enhancement = use_enhancement and (i == block_num - 1)
            
            if use_block_enhancement:
                block = LightweightCrackEnhancedHGBlock(
                    in_chs if i == 0 else out_chs,
                    mid_chs,
                    out_chs,
                    layer_num,
                    residual=False if i == 0 else True,
                    kernel_size=kernel_size,
                    light_block=light_block,
                    use_lab=use_lab,
                    agg=agg,
                    drop_path=drop_path[i] if isinstance(drop_path, (list, tuple)) else drop_path,
                )
            else:
                block = HG_Block(  # 使用原始Block
                    in_chs if i == 0 else out_chs,
                    mid_chs,
                    out_chs,
                    layer_num,
                    residual=False if i == 0 else True,
                    kernel_size=kernel_size,
                    light_block=light_block,
                    use_lab=use_lab,
                    agg=agg,
                    drop_path=drop_path[i] if isinstance(drop_path, (list, tuple)) else drop_path,
                )
            
            blocks_list.append(block)
        
        self.blocks = nn.Sequential(*blocks_list)


@register()
class HGNetv2LightweightEnhanced(HGNetv2):
    """
    轻量级增强版HGNetV2
    参数量控制在原始模型的130%以内（10M → 13M）
    """
    
    def __init__(self, name, use_lab=False, return_idx=[1, 2, 3],
                 freeze_stem_only=True, freeze_at=0, freeze_norm=True,
                 pretrained=True, local_model_dir='weight/hgnetv2/',
                 # 轻量级增强参数
                 enhance_stages=[2, 3],  # 只增强后两个stage，减少参数
                 enhancement_ratio=0.3): # 增强强度比例
        
        # 保存配置
        self._model_name = name
        self.enhance_stages = enhance_stages
        self.enhancement_ratio = enhancement_ratio
        
        # 先调用父类初始化，但暂时禁用预训练权重加载
        super().__init__(name, use_lab, return_idx, freeze_stem_only, 
                        freeze_at, freeze_norm, pretrained=False, local_model_dir=local_model_dir)
        
        # 重新构建stages，选择性使用轻量级增强
        self._rebuild_lightweight_enhanced_stages()
        
        # 智能加载预训练权重
        if pretrained:
            self._load_pretrained_weights_smart(name, local_model_dir)
    
    def _rebuild_lightweight_enhanced_stages(self):
        """重新构建stages，选择性使用轻量级增强"""
        stage_config = self.arch_configs[self._get_model_name()]['stage_config']
        
        self.stages = nn.ModuleList()
        for i, k in enumerate(stage_config):
            in_channels, mid_channels, out_channels, block_num, downsample, light_block, kernel_size, layer_num = stage_config[k]
            
            # 只在指定的stage使用增强
            use_enhancement = i in self.enhance_stages
            
            if use_enhancement:
                # 使用轻量级增强版Stage
                stage = LightweightCrackEnhancedHGStage(
                    in_channels, mid_channels, out_channels,
                    block_num, layer_num, downsample,
                    light_block, kernel_size, self.use_lab,
                    use_enhancement=True
                )
            else:
                # 使用原始Stage
                stage = HG_Stage(
                    in_channels, mid_channels, out_channels,
                    block_num, layer_num, downsample,
                    light_block, kernel_size, self.use_lab
                )
            
            self.stages.append(stage)
    
    def _load_pretrained_weights_smart(self, name, local_model_dir):
        """智能加载预训练权重"""
        import os
        
        try:
            model_path = local_model_dir + f'PPHGNetV2_{name}_stage1.pth'
            download_url = self.arch_configs[name]['url']
            
            if os.path.exists(model_path):
                state_dict = torch.load(model_path, map_location='cpu')
                print(f"从本地加载预训练权重: {model_path}")
            else:
                print(f"从URL下载预训练权重: {download_url}")
                state_dict = torch.hub.load_state_dict_from_url(download_url, map_location='cpu', model_dir=local_model_dir)
            
            # 智能权重匹配
            self._smart_load_state_dict(state_dict)
            print(f"✅ 成功加载预训练权重 (轻量级增强模式)")
            
        except Exception as e:
            print(f"⚠️ 预训练权重加载失败: {str(e)}")
            print(f"继续使用随机初始化权重训练")
    
    def _smart_load_state_dict(self, pretrained_state_dict):
        """智能加载状态字典"""
        model_state_dict = self.state_dict()
        
        loaded_count = 0
        skipped_count = 0
        
        for name, param in model_state_dict.items():
            if name in pretrained_state_dict:
                pretrained_param = pretrained_state_dict[name]
                if param.shape == pretrained_param.shape:
                    param.data.copy_(pretrained_param.data)
                    loaded_count += 1
                else:
                    skipped_count += 1
            else:
                # 增强模块的新参数，使用专门初始化
                if any(keyword in name for keyword in ['lightweight', 'direction_weight', 'detail_weight', 'contrast']):
                    skipped_count += 1
                else:
                    skipped_count += 1
        
        # 初始化增强模块
        self._initialize_lightweight_enhancement_modules()
        
        total_params = len(model_state_dict)
        print(f"权重加载统计: 成功{loaded_count}/{total_params} ({loaded_count/total_params*100:.1f}%)")
    
    def _initialize_lightweight_enhancement_modules(self):
        """初始化轻量级增强模块"""
        for name, module in self.named_modules():
            if 'lightweight' in name:
                if isinstance(module, nn.Conv2d):
                    nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
                    if module.bias is not None:
                        nn.init.constant_(module.bias, 0)
                elif isinstance(module, nn.BatchNorm2d):
                    nn.init.constant_(module.weight, 1)
                    nn.init.constant_(module.bias, 0)
        
        # 初始化权重参数
        for name, param in self.named_parameters():
            if any(keyword in name for keyword in ['direction_weight', 'detail_weight', 'contrast_weight']):
                nn.init.constant_(param, 0.1)
    
    def _get_model_name(self):
        return getattr(self, '_model_name', 'B4')
    
    def get_enhancement_info(self):
        """获取增强配置信息"""
        enhanced_stages = []
        total_enhanced_blocks = 0
        total_blocks = 0
        
        for i, stage in enumerate(self.stages):
            if isinstance(stage, LightweightCrackEnhancedHGStage):
                enhanced_stages.append(f"Stage{i+1}")
                # 统计增强的block数量
                for block in stage.blocks:
                    total_blocks += 1
                    if isinstance(block, LightweightCrackEnhancedHGBlock):
                        total_enhanced_blocks += 1
            else:
                for block in stage.blocks:
                    total_blocks += 1
        
        return {
            'enhanced_stages': enhanced_stages,
            'total_stages': len(self.stages),
            'enhanced_blocks': total_enhanced_blocks,
            'total_blocks': total_blocks,
            'enhancement_ratio': total_enhanced_blocks / total_blocks if total_blocks > 0 else 0,
            'parameter_overhead': 'Lightweight (~30% increase)'
        }
    
    def get_parameter_count(self):
        """获取参数统计"""
        total_params = sum(p.numel() for p in self.parameters())
        
        # 统计增强模块参数
        enhancement_params = 0
        for name, param in self.named_parameters():
            if any(keyword in name for keyword in ['lightweight', 'direction_weight', 'detail_weight', 'contrast']):
                enhancement_params += param.numel()
        
        return {
            'total_parameters': total_params,
            'enhancement_parameters': enhancement_params,
            'base_parameters': total_params - enhancement_params,
            'overhead_ratio': enhancement_params / (total_params - enhancement_params) if total_params > enhancement_params else 0
        }


# 预定义的轻量级增强版本
@register()
class HGNetv2LightweightCrackB0(HGNetv2LightweightEnhanced):
    """HGNetV2-B0 轻量级裂缝检测版本"""
    def __init__(self, **kwargs):
        super().__init__(name='B0', enhance_stages=[2, 3], **kwargs)


@register()
class HGNetv2LightweightCrackB1(HGNetv2LightweightEnhanced):
    """HGNetV2-B1 轻量级裂缝检测版本"""
    def __init__(self, **kwargs):
        super().__init__(name='B1', enhance_stages=[1, 2, 3], **kwargs)


@register()
class HGNetv2LightweightCrackS(HGNetv2LightweightEnhanced):
    """HGNetV2-S 轻量级裂缝检测版本 (对应B1)"""
    def __init__(self, **kwargs):
        super().__init__(name='B1', enhance_stages=[2, 3], **kwargs)