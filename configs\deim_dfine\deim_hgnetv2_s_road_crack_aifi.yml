__include__: [
  './dfine_hgnetv2_s_coco.yml',
  '../base/deim.yml',
  '../dataset/svrdd_detection.yml'
]

print_freq: 50
output_dir: /hy-tmp/outputs/deim_road_crack_aifi

# 针对道路裂缝检测的训练策略
epoches: 200  # 道路裂缝需要更多训练轮数

# 使用改进的HybridEncoderAIFI
DEIM:
  backbone: HGNetv2
  encoder: HybridEncoderAIFI
  decoder: DFINETransformer

# 针对道路裂缝优化的AIFI配置
HybridEncoderAIFI:
  # AIFI特定参数 - 针对裂缝特征优化
  use_aifi: True
  window_size: 8        # 8x8窗口，适合捕获裂缝的局部连续性
  shift_size: 4         # 50%重叠，增强跨窗口信息交互
  
  # 可选：多尺度AIFI（如果需要更强的多尺度能力）
  # use_multi_scale_aifi: True
  # window_sizes: [7, 14]  # 小窗口捕获细节，大窗口捕获整体结构

# 针对道路裂缝的训练参数优化
optimizer:
  type: AdamW
  params: 
    - 
      params: '^(?=.*backbone)(?!.*bn).*'
      lr: 0.0003  # 稍微降低backbone学习率，保持预训练特征
    - 
      params: '^(?=.*(?:encoder|decoder))(?!.*(?:norm|bn)).*'
      lr: 0.0008  # 提高encoder/decoder学习率，加速AIFI学习
    - 
      params: '^(?=.*(?:norm|bn)).*'
      weight_decay: 0.
  lr: 0.0006
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# 数据增强策略 - 适合道路场景
train_dataloader:
  total_batch_size: 12  # 适中的batch size
  dataset:
    transforms:
      ops:
        # 道路图像通常是横向的，增加水平翻转
        - {type: RandomHorizontalFlip, p: 0.5}
        # 亮度对比度增强，适应不同光照条件
        - {type: ColorJitter, brightness: 0.2, contrast: 0.2, saturation: 0.1, hue: 0.05}
        # 随机裁剪，增强局部特征学习
        - {type: RandomResizedCrop, size: [640, 640], scale: [0.8, 1.0]}
  collate_fn:
    mixup_epochs: [20, 180]  # 延长mixup使用时间
    stop_epoch: 180

val_dataloader:
  total_batch_size: 8
  num_workers: 4
  shuffle: False

# 学习率调度 - 针对长训练周期优化
lrsheduler: flatcosine
lr_gamma: 0.3  # 更强的学习率衰减
warmup_iter: 100  # 更长的warmup

# 早停和模型保存策略
early_stopping:
  monitor: val_loss
  patience: 15  # 更大的耐心值
  mode: min
  min_delta: 0.0001
  verbose: True

# 针对道路裂缝的损失权重调整
DEIMCriterion:
  weight_dict: {
    loss_vfl: 1.2,      # 增加分类损失权重，提高小目标检测
    loss_bbox: 6,       # 增加边界框损失权重，提高定位精度
    loss_giou: 2.5,     # 增加GIoU损失，提高形状匹配
    loss_fgl: 0.2,      # 适当增加focal loss权重
    loss_ddf: 1.8       # 增加密集检测损失
  }
  alpha: 0.8  # 调整focal loss参数，适应裂缝检测
  gamma: 2.5

# 后处理优化
PostProcessor:
  num_top_queries: 400  # 增加查询数量，提高召回率

# DFINETransformer针对裂缝检测的优化
DFINETransformer:
  num_queries: 400      # 增加查询数量
  num_denoising: 120    # 增加去噪查询
  label_noise_ratio: 0.4  # 降低标签噪声比例
  box_noise_scale: 0.8    # 降低边界框噪声