__include__: [
  '../dataset/coco_detection.yml',
  '../runtime.yml',
  '../base/dataloader.yml',
  '../base/optimizer.yml',
  '../base/dfine_hgnetv2.yml',
]

output_dir: ./outputs/dfine_hgnetv2_l_coco


HGNetv2:
  name: 'B4'
  return_idx: [1, 2, 3]
  freeze_stem_only: True
  freeze_at: 0
  freeze_norm: True

optimizer:
  type: AdamW
  params:
    -
      params: '^(?=.*backbone)(?!.*norm|bn).*$'
      lr: 0.0000125
    -
      params: '^(?=.*(?:encoder|decoder))(?=.*(?:norm|bn)).*$'
      weight_decay: 0.

  lr: 0.00025
  betas: [0.9, 0.999]
  weight_decay: 0.000125


# Increase to search for the optimal ema
epoches: 80 # 72 + 2n
train_dataloader:
  dataset:
    transforms:
      policy:
        epoch: 72
  collate_fn:
    stop_epoch: 72
    ema_restart_decay: 0.9999
    base_size_repeat: 4
