task: detection

evaluator:
  type: CocoEvaluator
  iou_types: ['bbox', ]

num_classes: 366
remap_mscoco_category: False

train_dataloader:
  type: DataLoader
  dataset:
    type: CocoDetection
    img_folder: /home/<USER>/objects365/train
    ann_file: /home/<USER>/objects365/train/new_zhiyuan_objv2_train_resized640.json
    return_masks: False
    transforms:
      type: Compose
      ops: ~
  shuffle: True
  num_workers: 4
  drop_last: True
  collate_fn:
    type: BatchImageCollateFunction


val_dataloader:
  type: DataLoader
  dataset:
    type: CocoDetection
    img_folder: /home/<USER>/objects365/val
    ann_file: /home/<USER>/objects365/val/new_zhiyuan_objv2_val_resized640.json
    return_masks: False
    transforms:
      type: Compose
      ops: ~
  shuffle: False
  num_workers: 4
  drop_last: False
  collate_fn:
    type: BatchImageCollateFunction
