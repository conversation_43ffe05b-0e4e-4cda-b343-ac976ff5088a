__include__: [
  './rtdetrv2_r50vd_6x_coco.yml',
  '../base/rt_deim.yml',
]

output_dir: ./outputs/deim_rtdetrv2_r50vd_60e_coco

optimizer:
  type: AdamW
  params: 
    - 
      params: '^(?=.*backbone)(?!.*norm).*$'
      lr: 0.00002
    - 
      params: '^(?=.*(?:norm|bn)).*$'
      weight_decay: 0.

  lr: 0.0002
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# change part
epoches: 60
flat_epoch: 34    # 4 + 60 / 2
no_aug_epoch: 2

train_dataloader: 
  dataset: 
    transforms:
      policy:
        epoch: [4, 34, 58]   # list 
      
  collate_fn:
    mixup_epochs: [4, 34]
    stop_epoch: 58