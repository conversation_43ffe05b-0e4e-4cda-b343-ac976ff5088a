"""
Enhanced AIFI (Advanced Improved Feature Integration) for Road Crack Detection
Copyright (c) 2024 The DEIM Authors. All Rights Reserved.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Optional, Tuple

from .windowed_attention import WindowedTransformerEncoderLayer, WindowedMultiHeadAttention


class EnhancedAIFI(nn.Module):
    """
    增强版AIFI，专门针对道路裂缝检测优化
    结合多尺度窗口、方向感知和自适应权重
    """
    
    def __init__(
        self,
        c: int,
        nhead: int = 8,
        window_sizes: list = [4, 7, 14],  # 多尺度窗口：细节、标准、全局
        use_directional: bool = True,     # 是否使用方向感知
        use_adaptive_weights: bool = True, # 是否使用自适应权重
        dropout: float = 0.1,
        act: str = "gelu"
    ):
        super().__init__()
        
        self.c = c
        self.nhead = nhead
        self.window_sizes = window_sizes
        self.use_directional = use_directional
        self.use_adaptive_weights = use_adaptive_weights
        
        # 多尺度AIFI模块
        self.multi_scale_aifi = nn.ModuleList([
            WindowedTransformerEncoderLayer(
                d_model=c,
                nhead=nhead,
                dim_feedforward=c * 4,
                dropout=dropout,
                activation=act,
                window_size=ws,
                shift_size=ws // 2
            ) for ws in window_sizes
        ])
        
        # 方向感知注意力（针对裂缝的线性特征）
        if use_directional:
            self.directional_weights = nn.Parameter(torch.ones(3) / 3)  # 水平、垂直、对角
            self.direction_proj = nn.ModuleList([
                nn.Linear(c, c) for _ in range(3)  # 3个方向的投影
            ])
        
        # 自适应权重学习
        if use_adaptive_weights:
            self.scale_attention = nn.Sequential(
                nn.AdaptiveAvgPool2d(1),
                nn.Conv2d(c, len(window_sizes), 1),
                nn.Softmax(dim=1)
            )
        
        # 特征融合
        fusion_dim = c * len(window_sizes)
        if use_directional:
            fusion_dim += c  # 添加方向特征
            
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(fusion_dim, c * 2, 1, bias=False),
            nn.BatchNorm2d(c * 2),
            nn.GELU(),
            nn.Conv2d(c * 2, c, 1, bias=False),
            nn.BatchNorm2d(c)
        )
        
        # 残差连接权重
        self.gamma = nn.Parameter(torch.ones(1) * 0.1)  # 初始化为较小值
        
    def _apply_directional_attention(self, x, hw_shape):
        """应用方向感知注意力"""
        if not self.use_directional:
            return None
            
        B, L, C = x.shape
        H, W = hw_shape
        
        # 重塑为2D
        x_2d = x.view(B, H, W, C).permute(0, 3, 1, 2)  # (B, C, H, W)
        
        # 三个方向的特征提取
        features = []
        
        # 水平方向 (适合水平裂缝)
        h_feat = F.avg_pool2d(x_2d, kernel_size=(1, 3), stride=1, padding=(0, 1))
        features.append(self.direction_proj[0](h_feat.permute(0, 2, 3, 1).flatten(1, 2)))
        
        # 垂直方向 (适合垂直裂缝)  
        v_feat = F.avg_pool2d(x_2d, kernel_size=(3, 1), stride=1, padding=(1, 0))
        features.append(self.direction_proj[1](v_feat.permute(0, 2, 3, 1).flatten(1, 2)))
        
        # 对角方向 (适合斜向裂缝)
        d_feat = F.avg_pool2d(x_2d, kernel_size=3, stride=1, padding=1)
        features.append(self.direction_proj[2](d_feat.permute(0, 2, 3, 1).flatten(1, 2)))
        
        # 加权融合
        weighted_features = []
        for i, feat in enumerate(features):
            weighted_features.append(feat * self.directional_weights[i])
        
        directional_feat = sum(weighted_features)
        return directional_feat
    
    def forward(self, x, hw_shape=None):
        """
        Args:
            x: 输入特征 (B, L, C) 或 (B, C, H, W)
            hw_shape: 空间尺寸 (H, W)
        """
        input_is_2d = len(x.shape) == 4
        
        if input_is_2d:
            B, C, H, W = x.shape
            hw_shape = (H, W)
            x_seq = x.flatten(2).transpose(1, 2)  # (B, H*W, C)
        else:
            B, L, C = x.shape
            x_seq = x
            if hw_shape is None:
                H = W = int(math.sqrt(L))
                hw_shape = (H, W)
            else:
                H, W = hw_shape
        
        # 多尺度AIFI特征
        multi_scale_features = []
        for aifi_layer in self.multi_scale_aifi:
            feat = aifi_layer(x_seq, hw_shape=hw_shape)
            multi_scale_features.append(feat)
        
        # 方向感知特征
        directional_feat = self._apply_directional_attention(x_seq, hw_shape)
        
        # 转换为2D进行融合
        H, W = hw_shape
        features_2d = []
        
        for feat in multi_scale_features:
            feat_2d = feat.transpose(1, 2).view(B, C, H, W)
            features_2d.append(feat_2d)
        
        if directional_feat is not None:
            dir_feat_2d = directional_feat.transpose(1, 2).view(B, C, H, W)
            features_2d.append(dir_feat_2d)
        
        # 拼接所有特征
        concat_feat = torch.cat(features_2d, dim=1)
        
        # 自适应权重
        if self.use_adaptive_weights and input_is_2d:
            scale_weights = self.scale_attention(x)  # (B, num_scales, 1, 1)
            
            # 对多尺度特征应用权重
            weighted_multi_scale = []
            for i, feat in enumerate(features_2d[:len(self.window_sizes)]):
                weighted_multi_scale.append(feat * scale_weights[:, i:i+1])
            
            # 重新拼接
            if directional_feat is not None:
                concat_feat = torch.cat(weighted_multi_scale + [features_2d[-1]], dim=1)
            else:
                concat_feat = torch.cat(weighted_multi_scale, dim=1)
        
        # 特征融合
        fused_feat = self.feature_fusion(concat_feat)
        
        # 残差连接
        if input_is_2d:
            output = x + self.gamma * fused_feat
            return output
        else:
            output_seq = fused_feat.flatten(2).transpose(1, 2)
            return x_seq + self.gamma * output_seq


class CrackAdaptiveAIFI(nn.Module):
    """
    专门针对裂缝检测的自适应AIFI
    根据特征图内容动态调整窗口大小和注意力模式
    """
    
    def __init__(
        self,
        c: int,
        nhead: int = 8,
        base_window_size: int = 7,
        adaptive_range: tuple = (4, 14),  # 窗口大小范围
        dropout: float = 0.1
    ):
        super().__init__()
        
        self.c = c
        self.nhead = nhead
        self.base_window_size = base_window_size
        self.adaptive_range = adaptive_range
        
        # 窗口大小预测器
        self.window_predictor = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(c, c // 4, 1),
            nn.ReLU(),
            nn.Conv2d(c // 4, 1, 1),
            nn.Sigmoid()
        )
        
        # 多个候选AIFI（不同窗口大小）
        self.candidate_aifi = nn.ModuleDict({
            str(ws): WindowedTransformerEncoderLayer(
                d_model=c,
                nhead=nhead,
                dim_feedforward=c * 4,
                dropout=dropout,
                activation="gelu",
                window_size=ws,
                shift_size=ws // 2
            ) for ws in range(adaptive_range[0], adaptive_range[1] + 1, 2)
        })
        
        # 裂缝密度估计器
        self.crack_density_estimator = nn.Sequential(
            nn.Conv2d(c, c // 2, 3, padding=1),
            nn.ReLU(),
            nn.Conv2d(c // 2, 1, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x, hw_shape=None):
        """自适应选择最优的AIFI配置"""
        input_is_2d = len(x.shape) == 4
        
        if input_is_2d:
            B, C, H, W = x.shape
            hw_shape = (H, W)
            x_seq = x.flatten(2).transpose(1, 2)
        else:
            B, L, C = x.shape
            x_seq = x
            if hw_shape is None:
                H = W = int(math.sqrt(L))
                hw_shape = (H, W)
            else:
                H, W = hw_shape
            x = x_seq.transpose(1, 2).view(B, C, H, W)
        
        # 预测最优窗口大小
        window_weight = self.window_predictor(x).squeeze()  # (B,)
        optimal_window_size = (
            self.adaptive_range[0] + 
            window_weight * (self.adaptive_range[1] - self.adaptive_range[0])
        ).round().int()
        
        # 估计裂缝密度
        crack_density = self.crack_density_estimator(x)  # (B, 1, H, W)
        
        # 根据裂缝密度调整注意力强度
        attention_scale = 1.0 + crack_density.mean(dim=[2, 3], keepdim=True) * 0.5
        
        # 选择最接近的AIFI配置
        outputs = []
        for i in range(B):
            ws = optimal_window_size[i].item()
            # 找到最接近的可用窗口大小
            available_sizes = [int(k) for k in self.candidate_aifi.keys()]
            closest_ws = min(available_sizes, key=lambda x: abs(x - ws))
            
            # 应用对应的AIFI
            aifi_layer = self.candidate_aifi[str(closest_ws)]
            sample_output = aifi_layer(
                x_seq[i:i+1], 
                hw_shape=hw_shape
            )
            
            # 应用注意力缩放
            scale = attention_scale[i].view(1, 1, 1)
            sample_output = sample_output * scale
            
            outputs.append(sample_output)
        
        output = torch.cat(outputs, dim=0)
        
        if input_is_2d:
            return output.transpose(1, 2).view(B, C, H, W)
        else:
            return output