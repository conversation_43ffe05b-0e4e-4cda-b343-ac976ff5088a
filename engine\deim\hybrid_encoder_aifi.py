"""
DEIM: DETR with Improved Matching for Fast Convergence
Copyright (c) 2024 The DEIM Authors. All Rights Reserved.
---------------------------------------------------------------------------------
Improved HybridEncoder with AIFI (Advanced Improved Feature Integration)
Based on Windowed Self-Attention for better feature processing
"""

import copy
from collections import OrderedDict
import torch
import torch.nn as nn
import torch.nn.functional as F

# 完整继承父类的所有依赖
from .hybrid_encoder import (
    HybridEncoder, 
    TransformerEncoderLayer, 
    TransformerEncoder,
    ConvNormLayer_fuse,
    ConvNormLayer,
    SCDown,
    VGGBlock,
    CSPLayer,
    RepNCSPELAN4
)
from .windowed_attention import WindowedTransformerEncoderLayer
from .utils import get_activation
from ..core import register

__all__ = ['HybridEncoderAIFI']

@register()
class HybridEncoderAIFI(HybridEncoder):
    """
    改进的HybridEncoder，集成AIFI (Advanced Improved Feature Integration)
    继承自原始HybridEncoder，只替换encoder部分使用AIFI
    """
    
    def __init__(
        self,
        # 显式接收所有必要参数，避免依赖父类属性保存
        nhead=8,
        dim_feedforward=1024,
        dropout=0.0,
        enc_act='gelu',
        # AIFI specific parameters
        use_aifi=True,
        window_size=7,
        shift_size=0,
        **kwargs
    ):
        # 调用父类构造函数
        super().__init__(
            nhead=nhead,
            dim_feedforward=dim_feedforward,
            dropout=dropout,
            enc_act=enc_act,
            **kwargs
        )
        
        # 确保这些属性在子类中也可用（防止父类没有保存）
        self.nhead = nhead
        self.dim_feedforward = dim_feedforward
        self.dropout = dropout
        self.enc_act = enc_act
        
        # AIFI specific parameters
        self.use_aifi = use_aifi
        self.window_size = window_size
        self.shift_size = shift_size
        
        # 重新构建encoder以使用AIFI
        if use_aifi and self.num_encoder_layers > 0:
            self._build_aifi_encoder()
    
    def _build_aifi_encoder(self):
        """构建基于增强AIFI的编码器，替换原有的encoder"""
        from .enhanced_aifi import EnhancedAIFI
        
        # 创建增强版AIFI编码器层
        aifi_encoder_layer = EnhancedAIFI(
            c=self.hidden_dim,
            nhead=self.nhead,
            window_sizes=[4, 7, 14],  # 多尺度窗口
            use_directional=True,     # 启用方向感知
            use_adaptive_weights=True, # 启用自适应权重
            dropout=self.dropout,
            act=self.enc_act
        )
        
        # 重新构建encoder
        self.encoder = nn.ModuleList([
            nn.ModuleList([copy.deepcopy(aifi_encoder_layer) for _ in range(self.num_encoder_layers)]) 
            for _ in range(len(self.use_encoder_idx))
        ])
    
    def forward(self, feats):
        """
        前向传播，只修改encoder部分使用AIFI，其他部分继承父类
        """
        assert len(feats) == len(self.in_channels)
        proj_feats = [self.input_proj[i](feat) for i, feat in enumerate(feats)]

        # encoder with AIFI
        if self.num_encoder_layers > 0 and self.use_aifi:
            for i, enc_ind in enumerate(self.use_encoder_idx):
                h, w = proj_feats[enc_ind].shape[2:]
                # flatten [B, C, H, W] to [B, HxW, C]
                src_flatten = proj_feats[enc_ind].flatten(2).permute(0, 2, 1)
                
                # 构建位置编码
                if self.training or self.eval_spatial_size is None:
                    pos_embed = self.build_2d_sincos_position_embedding(
                        w, h, self.hidden_dim, self.pe_temperature).to(src_flatten.device)
                else:
                    pos_embed = getattr(self, f'pos_embed{enc_ind}', None)
                    if pos_embed is not None:
                        pos_embed = pos_embed.to(src_flatten.device)

                # 使用增强AIFI编码器处理特征
                memory = src_flatten
                for layer in self.encoder[i]:
                    # EnhancedAIFI不需要pos_embed参数，它内部处理位置信息
                    memory = layer(memory, hw_shape=(h, w))
                proj_feats[enc_ind] = memory.permute(0, 2, 1).reshape(-1, self.hidden_dim, h, w).contiguous()
        else:
            # 回退到父类的encoder处理
            return super().forward(feats)

        # 其他部分完全继承父类的处理逻辑
        # broadcasting and fusion
        inner_outs = [proj_feats[-1]]
        for idx in range(len(self.in_channels) - 1, 0, -1):
            feat_heigh = inner_outs[0]
            feat_low = proj_feats[idx - 1]
            feat_heigh = self.lateral_convs[len(self.in_channels) - 1 - idx](feat_heigh)
            inner_outs[0] = feat_heigh
            upsample_feat = F.interpolate(feat_heigh, scale_factor=2., mode='nearest')
            inner_out = self.fpn_blocks[len(self.in_channels)-1-idx](torch.cat([upsample_feat, feat_low], dim=1))
            inner_outs.insert(0, inner_out)

        outs = [inner_outs[0]]
        for idx in range(len(self.in_channels) - 1):
            feat_low = outs[-1]
            feat_height = inner_outs[idx + 1]
            downsample_feat = self.downsample_convs[idx](feat_low)
            out = self.pan_blocks[idx](torch.cat([downsample_feat, feat_height], dim=1))
            outs.append(out)

        return outs