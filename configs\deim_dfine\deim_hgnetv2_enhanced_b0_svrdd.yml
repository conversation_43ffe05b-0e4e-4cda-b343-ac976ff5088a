__include__: [
  './dfine_hgnetv2_s_coco.yml',
  '../base/deim.yml',
  '../dataset/svrdd_detection.yml'
]

print_freq: 100
output_dir: /hy-tmp/outputs/deim_enhanced_b0_svrdd

epoches: 150

# ===== 增强版配置：只修改必要的部分 =====

# 使用增强版HGNetV2-B0
DEIM:
  backbone: HGNetv2Enhanced  # 唯一必须的改动

# 增强版HGNetV2配置（只添加增强相关参数，其他保持与原始一致）
HGNetv2Enhanced:
  name: 'B0'  # 与原始配置一致
  return_idx: [1, 2, 3]
  freeze_at: -1
  freeze_norm: False
  use_lab: True
  
  # 增强模块专用配置（新增）
  enhance_all_stages: True        # 增强所有stage
  enhance_stage_idx: [1, 2, 3]    # 备选配置

# ===== 以下配置与原始deim_hgnetv2_s_svrdd.yml保持完全一致 =====

optimizer:
  type: AdamW
  params: 
    - 
      params: '^(?=.*backbone)(?!.*bn).*'
      lr: 0.0005  # 与原始配置一致
    - 
      params: '^(?=.*(?:norm|bn)).*'     # except bias
      weight_decay: 0.
  lr: 0.0005
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# batch size - 与原始一致
train_dataloader:
  total_batch_size: 16
  collate_fn:
    mixup_epochs: [10, 160]  # 与原始一致
    stop_epoch: 160

val_dataloader:
  total_batch_size: 16
  num_workers: 2
  shuffle: False

lrsheduler: flatcosine
lr_gamma: 0.5
warmup_iter: 50  # 前50个epoch进行warmup

# 早停配置 - 与原始一致
early_stopping:
  monitor: val_loss   # 监控的指标，可根据实际情况调整
  patience: 10       # 容忍多少个epoch无提升
  mode: min          # 监控指标的优化方向
  min_delta: 0.0001  # 最小变化量
  verbose: True      # 是否打印日志