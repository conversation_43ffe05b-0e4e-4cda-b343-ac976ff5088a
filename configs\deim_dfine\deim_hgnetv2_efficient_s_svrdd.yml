__include__: [
  './dfine_hgnetv2_s_coco.yml',
  '../base/deim.yml',
  '../dataset/svrdd_detection.yml'
]
print_freq: 100
output_dir: /hy-tmp/outputs/deim_efficient

epoches: 150

# 配置backbone
backbone:
  type: HGNetv2Efficient
  name: 'B0'  # 使用与原始模型相同的架构配置
  use_lab: False
  return_idx: [1, 2, 3]
  freeze_stem_only: True
  freeze_at: 0
  freeze_norm: True
  pretrained: True

optimizer:
  type: AdamW
  params: 
    - 
      params: '^(?=.*backbone)(?!.*bn).*$'
      lr: 0.0005
    - 
      params: '^(?=.*(?:norm|bn)).*$'     # except bias
      weight_decay: 0.
  lr: 0.0005
  betas: [0.9, 0.999]
  weight_decay: 0.0001

# batch size
train_dataloader:
  total_batch_size: 16
  collate_fn:
    mixup_epochs: [10, 160]
    stop_epoch: 160

val_dataloader:
  total_batch_size: 16
  num_workers: 2
  shuffle: False

lrsheduler: flatcosine
lr_gamma: 0.5
warmup_iter: 50  # 前50个epoch进行warmup

# 早停配置
early_stopping:
  monitor: val_loss   # 监控的指标
  patience: 10        # 容忍多少个epoch无提升
  mode: min          # 监控指标的优化方向
  min_delta: 0.0001  # 最小变化量
  verbose: True      # 是否打印日志
