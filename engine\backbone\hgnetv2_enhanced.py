"""
Enhanced HGNetV2 for Crack Detection
继承原始HGNetV2，添加裂缝检测专用增强
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from .hgnetv2 import HGNetv2, HG_Stage, HG_Block, ConvBNAct, StemBlock, EseModule
from ..core import register


class CrackEnhancedHGBlock(HG_Block):
    """
    裂缝增强版HG_Block
    继承原始HG_Block，在forward中添加裂缝专用处理
    """
    def __init__(self, in_chs, mid_chs, out_chs, layer_num, **kwargs):
        # 完全继承父类初始化
        super().__init__(in_chs, mid_chs, out_chs, layer_num, **kwargs)
        
        # ===== 裂缝专用增强模块 =====
        
        # 1. 裂缝方向感知 - 针对线性裂缝的长条形卷积
        # 确保通道数能被3整除，避免除法余数问题
        direction_chs = out_chs // 3
        remaining_chs = out_chs - direction_chs * 2  # 处理不能整除的情况
        
        self.crack_direction_convs = nn.ModuleList([
            # 水平裂缝检测 - 1×11卷积核
            nn.Conv2d(out_chs, direction_chs, (1, 11), padding=(0, 5)),
            # 垂直裂缝检测 - 11×1卷积核  
            nn.Conv2d(out_chs, direction_chs, (11, 1), padding=(5, 0)),
            # 斜向裂缝检测 - 7×7卷积核 (处理余数)
            nn.Conv2d(out_chs, remaining_chs, 7, padding=3)
        ])
        
        # 2. 细节增强 - 提升低对比度裂缝特征
        self.detail_enhance = nn.Sequential(
            # 深度可分离卷积增强边缘
            nn.Conv2d(out_chs, out_chs, 3, padding=1, groups=out_chs),
            nn.BatchNorm2d(out_chs),
            nn.ReLU(inplace=True),
            # 1×1卷积整合特征
            nn.Conv2d(out_chs, out_chs, 1),
            nn.BatchNorm2d(out_chs)
        )
        
        # 3. 对比度自适应 - 根据局部对比度调整特征强度
        self.contrast_adaptive = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(out_chs, out_chs//4, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_chs//4, out_chs, 1),
            nn.Sigmoid()
        )
        
        # 4. 特征融合 - 整合所有增强特征
        # 计算融合通道数：原始(out_chs) + 方向感知(out_chs) + 细节增强(out_chs)
        fusion_channels = out_chs * 3  # 3倍输出通道数
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(fusion_channels, out_chs, 1, bias=False),
            nn.BatchNorm2d(out_chs),
            nn.ReLU(inplace=True)
        )
        
        # 5. 自适应融合权重 - 可学习的增强强度
        self.enhancement_weight = nn.Parameter(torch.tensor(0.2))
    
    def forward(self, x):
        # ===== 步骤1: 获得原始HG_Block输出 =====
        original_output = super().forward(x)
        
        # ===== 步骤2: 裂缝方向感知增强 =====
        direction_feats = []
        for conv in self.crack_direction_convs:
            direction_feats.append(conv(original_output))
        direction_feat = torch.cat(direction_feats, dim=1)  # 拼接三个方向
        
        # ===== 步骤3: 细节增强处理 =====
        detail_feat = self.detail_enhance(original_output)
        
        # ===== 步骤4: 对比度自适应调整 =====
        contrast_weight = self.contrast_adaptive(original_output)
        adapted_detail = detail_feat * contrast_weight
        
        # ===== 步骤5: 特征融合 =====
        enhanced_features = torch.cat([
            original_output,    # 保留原始特征
            direction_feat,     # 方向感知特征
            adapted_detail      # 自适应细节特征
        ], dim=1)
        
        fused_feat = self.feature_fusion(enhanced_features)
        
        # ===== 步骤6: 自适应残差连接 =====
        final_output = original_output + self.enhancement_weight * fused_feat
        
        return final_output


class CrackEnhancedHGStage(HG_Stage):
    """
    裂缝增强版HG_Stage
    继承原始HG_Stage，只替换HG_Block为CrackEnhancedHGBlock
    """
    def __init__(self, in_chs, mid_chs, out_chs, block_num, layer_num, 
                 downsample=True, light_block=False, kernel_size=3, 
                 use_lab=False, agg='se', drop_path=0.):
        
        # 先初始化父类的downsample部分
        super().__init__(in_chs, mid_chs, out_chs, block_num, layer_num,
                        downsample, light_block, kernel_size, use_lab, agg, drop_path)
        
        # 重新构建blocks，使用CrackEnhancedHGBlock替换HG_Block
        blocks_list = []
        for i in range(block_num):
            blocks_list.append(
                CrackEnhancedHGBlock(  # 使用增强版Block
                    in_chs if i == 0 else out_chs,
                    mid_chs,
                    out_chs,
                    layer_num,
                    residual=False if i == 0 else True,
                    kernel_size=kernel_size,
                    light_block=light_block,
                    use_lab=use_lab,
                    agg=agg,
                    drop_path=drop_path[i] if isinstance(drop_path, (list, tuple)) else drop_path,
                )
            )
        self.blocks = nn.Sequential(*blocks_list)


@register()
class HGNetv2Enhanced(HGNetv2):
    """
    裂缝检测增强版HGNetV2
    继承原始HGNetv2，使用CrackEnhancedHGStage替换原始HG_Stage
    集成了针对裂缝检测的优化配置
    """
    
    def __init__(self, name, use_lab=False, return_idx=[1, 2, 3],
                 freeze_stem_only=True, freeze_at=0, freeze_norm=True,
                 pretrained=True, local_model_dir='weight/hgnetv2/',
                 # 新增参数
                 enhance_all_stages=True,      # 是否增强所有stage
                 enhance_stage_idx=[1, 2, 3], # 指定增强哪些stage
                 # 集成优化配置
                 auto_optimize_for_crack=True): # 是否自动应用裂缝检测优化
        
        # 保存模型名称和配置
        self._model_name = name
        self.enhance_all_stages = enhance_all_stages
        self.enhance_stage_idx = enhance_stage_idx
        self.auto_optimize_for_crack = auto_optimize_for_crack
        
        # 先调用父类初始化，但暂时禁用预训练权重加载
        super().__init__(name, use_lab, return_idx, freeze_stem_only, 
                        freeze_at, freeze_norm, pretrained=False, local_model_dir=local_model_dir)
        
        # 重新构建stages，使用增强版Stage
        self._rebuild_enhanced_stages()
        
        # 智能加载预训练权重（如果需要）
        if pretrained:
            self._load_pretrained_weights_smart(name, local_model_dir)
    
    def _load_pretrained_weights_smart(self, name, local_model_dir):
        """
        智能加载预训练权重
        只加载兼容的权重，跳过增强模块的新增参数
        """
        import os
        import logging
        
        RED, GREEN, RESET = "\033[91m", "\033[92m", "\033[0m"
        
        try:
            # 获取预训练权重文件路径
            model_path = local_model_dir + f'PPHGNetV2_{name}_stage1.pth'
            download_url = self.arch_configs[name]['url']
            
            # 加载预训练权重
            if os.path.exists(model_path):
                state_dict = torch.load(model_path, map_location='cpu')
                print(f"从本地加载预训练权重: {model_path}")
            else:
                if torch.distributed.is_initialized() and torch.distributed.get_rank() == 0:
                    print(GREEN + f"从URL下载预训练权重: {download_url}" + RESET)
                    state_dict = torch.hub.load_state_dict_from_url(download_url, map_location='cpu', model_dir=local_model_dir)
                    torch.distributed.barrier()
                else:
                    if torch.distributed.is_initialized():
                        torch.distributed.barrier()
                    state_dict = torch.load(model_path, map_location='cpu')
            
            # 智能权重匹配和加载
            self._smart_load_state_dict(state_dict)
            
            print(f"✅ 成功加载预训练权重 (智能匹配模式)")
            
        except Exception as e:
            if not torch.distributed.is_initialized() or torch.distributed.get_rank() == 0:
                print(f"{RED}⚠️  预训练权重加载失败: {str(e)}{RESET}")
                print(f"{GREEN}继续使用随机初始化权重训练{RESET}")
    
    def _smart_load_state_dict(self, pretrained_state_dict):
        """
        智能加载状态字典
        策略：
        1. 完全匹配的参数直接加载
        2. 增强模块的新参数跳过（使用随机初始化）
        3. 形状不匹配的参数跳过并警告
        """
        model_state_dict = self.state_dict()
        
        # 统计信息
        loaded_count = 0
        skipped_count = 0
        shape_mismatch_count = 0
        
        print("🔄 开始智能权重匹配...")
        
        for name, param in model_state_dict.items():
            if name in pretrained_state_dict:
                pretrained_param = pretrained_state_dict[name]
                
                # 检查形状是否匹配
                if param.shape == pretrained_param.shape:
                    # 形状匹配，直接加载
                    param.data.copy_(pretrained_param.data)
                    loaded_count += 1
                else:
                    # 形状不匹配，跳过
                    print(f"⚠️  形状不匹配跳过: {name} "
                          f"模型:{param.shape} vs 预训练:{pretrained_param.shape}")
                    shape_mismatch_count += 1
            else:
                # 预训练权重中没有此参数（通常是增强模块的新参数）
                if 'enhancement' in name or 'crack_direction' in name or 'detail_enhance' in name or 'contrast_adaptive' in name:
                    # 这是增强模块的参数，使用默认初始化
                    skipped_count += 1
                else:
                    # 其他缺失参数，给出警告
                    print(f"⚠️  预训练权重中缺失: {name}")
                    skipped_count += 1
        
        # 打印加载统计
        total_params = len(model_state_dict)
        print(f"📊 权重加载统计:")
        print(f"   总参数数量: {total_params}")
        print(f"   ✅ 成功加载: {loaded_count}")
        print(f"   ⏭️  跳过参数: {skipped_count} (增强模块新参数)")
        print(f"   ⚠️  形状不匹配: {shape_mismatch_count}")
        print(f"   📈 加载比例: {loaded_count/total_params*100:.1f}%")
        
        # 特别处理增强模块的初始化
        self._initialize_enhancement_modules()
    
    def _initialize_enhancement_modules(self):
        """
        初始化增强模块的参数
        使用合适的初始化策略确保训练稳定性
        """
        print("🔧 初始化增强模块参数...")
        
        for name, module in self.named_modules():
            if any(keyword in name for keyword in ['crack_direction', 'detail_enhance', 'contrast_adaptive', 'feature_fusion']):
                if isinstance(module, nn.Conv2d):
                    # 卷积层使用Kaiming初始化
                    nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
                    if module.bias is not None:
                        nn.init.constant_(module.bias, 0)
                elif isinstance(module, nn.BatchNorm2d):
                    # BN层初始化
                    nn.init.constant_(module.weight, 1)
                    nn.init.constant_(module.bias, 0)
                elif isinstance(module, nn.Linear):
                    # 线性层初始化
                    nn.init.normal_(module.weight, 0, 0.01)
                    if module.bias is not None:
                        nn.init.constant_(module.bias, 0)
        
        # 特别初始化enhancement_weight参数
        for name, param in self.named_parameters():
            if 'enhancement_weight' in name:
                # 初始化为较小值，让模型逐渐学习增强强度
                nn.init.constant_(param, 0.1)
                print(f"   初始化 {name} = 0.1")
        
        print("✅ 增强模块参数初始化完成")
    
    def _rebuild_enhanced_stages(self):
        """重新构建stages，使用增强版HG_Stage"""
        stage_config = self.arch_configs[self._get_model_name()]['stage_config']
        
        self.stages = nn.ModuleList()
        for i, k in enumerate(stage_config):
            in_channels, mid_channels, out_channels, block_num, downsample, light_block, kernel_size, layer_num = stage_config[k]
            
            # 判断是否使用增强版Stage
            use_enhanced = self.enhance_all_stages or i in self.enhance_stage_idx
            
            if use_enhanced:
                # 使用裂缝增强版Stage
                stage = CrackEnhancedHGStage(
                    in_channels, mid_channels, out_channels,
                    block_num, layer_num, downsample,
                    light_block, kernel_size, self.use_lab
                )
            else:
                # 使用原始Stage
                stage = HG_Stage(
                    in_channels, mid_channels, out_channels,
                    block_num, layer_num, downsample,
                    light_block, kernel_size, self.use_lab
                )
            
            self.stages.append(stage)
    
    def _get_model_name(self):
        """从arch_configs中推断模型名称"""
        # 这里需要根据实际情况获取模型名称
        # 可以在__init__中保存name参数
        return getattr(self, '_model_name', 'B4')  # 默认B4
    
    def get_enhancement_info(self):
        """获取增强配置信息"""
        enhanced_stages = []
        for i, stage in enumerate(self.stages):
            if isinstance(stage, CrackEnhancedHGStage):
                enhanced_stages.append(f"Stage{i+1}")
        
        return {
            'enhanced_stages': enhanced_stages,
            'total_stages': len(self.stages),
            'enhancement_ratio': len(enhanced_stages) / len(self.stages),
            'auto_optimize_for_crack': self.auto_optimize_for_crack
        }
    
    def get_crack_optimized_config(self):
        """
        获取针对裂缝检测的优化配置
        当auto_optimize_for_crack=True时，返回推荐的训练配置
        """
        if not self.auto_optimize_for_crack:
            return {}
        
        # 针对裂缝检测的优化配置
        optimized_config = {
            # 优化的损失权重
            'DEIMCriterion': {
                'weight_dict': {
                    'loss_vfl': 1.0,      # 分类损失
                    'loss_bbox': 8.0,     # 大幅增加边界框损失权重（裂缝定位极其重要）
                    'loss_giou': 3.0,     # 增加GIoU损失权重
                    'loss_fgl': 0.25,     # 微调焦点损失
                    'loss_ddf': 2.0       # 增加密集检测损失
                },
                'alpha': 0.8,           # 调整focal loss alpha（更关注难样本）
                'gamma': 2.2,           # 调整focal loss gamma
                'matcher': {
                    'weight_dict': {
                        'cost_class': 2, 
                        'cost_bbox': 8,     # 匹配时也更重视边界框精度
                        'cost_giou': 3      # 匹配时重视IoU
                    }
                }
            },
            
            # 优化的学习率策略
            'optimizer_params': [
                {
                    'params': '^(?=.*backbone)(?!.*norm|bn).*',
                    'lr': 0.0003  # backbone稍微降低学习率，更稳定
                },
                {
                    'params': '^(?=.*backbone)(?=.*norm|bn).*',
                    'lr': 0.0003,
                    'weight_decay': 0.
                },
                {
                    'params': '^(?=.*enhancement).*',  # 增强模块使用更高学习率
                    'lr': 0.0008
                }
            ],
            
            # 优化的训练调度
            'training_schedule': {
                'lrsheduler': 'flatcosine',
                'lr_gamma': 0.3,          # 更激进的学习率衰减
                'warmup_iter': 80,        # 更长的warmup，让增强模块充分预热
                'mixup_epochs': [8, 130], # 调整mixup时机
                'stop_epoch': 130,        # 提前停止数据增强
                'early_stopping': {
                    'patience': 15,       # 增强模块需要更多时间收敛
                    'min_delta': 0.00005  # 更精细的停止条件
                }
            },
            
            # 针对裂缝的数据增强
            'data_augmentation': [
                {'type': 'RandomBrightnessContrast', 'brightness_limit': 0.2, 'contrast_limit': 0.3, 'p': 0.4},
                {'type': 'RandomGamma', 'gamma_limit': [85, 115], 'p': 0.3},
                {'type': 'GaussNoise', 'var_limit': [5, 25], 'p': 0.2}
            ]
        }
        
        return optimized_config


# 为了方便使用，提供一些预定义配置
@register()
class HGNetv2CrackB4(HGNetv2Enhanced):
    """HGNetV2-B4 裂缝检测专用版本"""
    def __init__(self, **kwargs):
        super().__init__(name='B4', **kwargs)


@register() 
class HGNetv2CrackL(HGNetv2Enhanced):
    """HGNetV2-L 裂缝检测专用版本"""
    def __init__(self, **kwargs):
        super().__init__(name='B5', **kwargs)  # B5对应L规模


@register()
class HGNetv2CrackS(HGNetv2Enhanced):
    """HGNetV2-S 裂缝检测专用版本"""
    def __init__(self, **kwargs):
        super().__init__(name='B1', **kwargs)  # B1对应S规模