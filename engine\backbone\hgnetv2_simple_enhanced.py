"""
Simple Enhanced HGNetV2 for Crack Detection
简洁版增强HGNetV2，最小化代码量，最大化继承优势
"""

import torch
import torch.nn as nn
from .hgnetv2 import HGNetv2, HG_Block
from ..core import register


class SimpleEnhancedHGBlock(HG_Block):
    """简洁版增强HG_Block - 只添加最核心的裂缝检测增强"""
    
    def __init__(self, in_chs, mid_chs, out_chs, layer_num, **kwargs):
        super().__init__(in_chs, mid_chs, out_chs, layer_num, **kwargs)
        
        # 极简增强：只添加2个小模块，避免通道数问题
        enhance_chs = max(out_chs // 8, 16)  # 确保最少16通道
        
        # 1. 方向感知（使用标准卷积，避免groups问题）
        self.direction_conv = nn.Sequential(
            nn.Conv2d(out_chs, enhance_chs, 3, padding=1, bias=False),
            nn.BatchNorm2d(enhance_chs),
            nn.ReLU(inplace=True)
        )
        
        # 2. 细节增强（1x1卷积）
        self.detail_conv = nn.Sequential(
            nn.Conv2d(out_chs, enhance_chs, 1, bias=False),
            nn.BatchNorm2d(enhance_chs),
            nn.ReLU(inplace=True)
        )
        
        # 3. 特征融合（投影回原始通道）
        self.fusion = nn.Sequential(
            nn.Conv2d(enhance_chs * 2, out_chs, 1, bias=False),
            nn.BatchNorm2d(out_chs)
        )
        
        # 可学习权重
        self.alpha = nn.Parameter(torch.tensor(0.1))
    
    def forward(self, x):
        # 调用父类forward
        base_out = super().forward(x)
        
        # 简单增强
        dir_feat = self.direction_conv(base_out)
        detail_feat = self.detail_conv(base_out)
        
        # 融合
        enhanced = self.fusion(torch.cat([dir_feat, detail_feat], dim=1))
        
        # 残差连接
        return base_out + self.alpha * enhanced


class SimpleEnhancementModule(nn.Module):
    """独立的增强模块，避免继承复杂性"""
    def __init__(self, channels):
        super().__init__()
        enhance_chs = max(channels // 8, 16)
        
        # 方向感知
        self.direction_conv = nn.Sequential(
            nn.Conv2d(channels, enhance_chs, 3, padding=1, bias=False),
            nn.BatchNorm2d(enhance_chs),
            nn.ReLU(inplace=True)
        )
        
        # 细节增强
        self.detail_conv = nn.Sequential(
            nn.Conv2d(channels, enhance_chs, 1, bias=False),
            nn.BatchNorm2d(enhance_chs),
            nn.ReLU(inplace=True)
        )
        
        # 特征融合
        self.fusion = nn.Sequential(
            nn.Conv2d(enhance_chs * 2, channels, 1, bias=False),
            nn.BatchNorm2d(channels)
        )
        
        # 可学习权重
        self.alpha = nn.Parameter(torch.tensor(0.1))
    
    def forward(self, x):
        dir_feat = self.direction_conv(x)
        detail_feat = self.detail_conv(x)
        enhanced = self.fusion(torch.cat([dir_feat, detail_feat], dim=1))
        return x + self.alpha * enhanced


@register()
class HGNetv2SimpleEnhanced(HGNetv2):
    """简洁版增强HGNetV2 - 使用独立增强模块，避免继承问题"""
    
    def __init__(self, name, enhance_stages=[2, 3], **kwargs):
        self.enhance_stages = enhance_stages
        self._model_name = name  # 保存模型名称
        super().__init__(name, **kwargs)
        
        # 为指定stage添加增强模块
        self._add_enhancement_modules()
    
    def _add_enhancement_modules(self):
        """为指定stage添加增强模块"""
        # 获取当前模型的配置
        model_name = getattr(self, '_model_name', 'B0')
        stage_config = self.arch_configs[model_name]['stage_config']
        
        self.enhancement_modules = nn.ModuleList()
        for i, k in enumerate(stage_config):
            if i in self.enhance_stages:
                # 获取该stage的输出通道数
                _, _, out_chs, _, _, _, _, _ = stage_config[k]
                # 添加对应的增强模块
                self.enhancement_modules.append(SimpleEnhancementModule(out_chs))
            else:
                self.enhancement_modules.append(None)
    
    def forward(self, x):
        """重写forward，在指定stage后添加增强"""
        x = self.stem(x)
        outs = []
        
        for idx, (stage, enhancement) in enumerate(zip(self.stages, self.enhancement_modules)):
            x = stage(x)
            
            # 如果该stage需要增强，应用增强模块
            if enhancement is not None:
                x = enhancement(x)
            
            if idx in self.return_idx:
                outs.append(x)
        
        return outs
    
    def get_enhancement_info(self):
        """获取增强信息"""
        return {
            'enhanced_stages': [f"Stage{i+1}" for i in self.enhance_stages],
            'total_stages': len(self.stages),
            'parameter_overhead': 'Minimal (~15% increase)'
        }


# 预定义版本
@register()
class HGNetv2SimpleEnhancedB0(HGNetv2SimpleEnhanced):
    def __init__(self, **kwargs):
        super().__init__(name='B0', enhance_stages=[2, 3], **kwargs)


@register() 
class HGNetv2SimpleEnhancedB1(HGNetv2SimpleEnhanced):
    def __init__(self, **kwargs):
        super().__init__(name='B1', enhance_stages=[2, 3], **kwargs)