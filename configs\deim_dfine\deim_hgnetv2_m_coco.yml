__include__: [
  './dfine_hgnetv2_m_coco.yml',
  '../base/deim.yml'
]

output_dir: ./outputs/deim_hgnetv2_m_coco

optimizer:
  type: AdamW
  params: 
    -
      params: '^(?=.*backbone)(?!.*bn).*$'
      lr: 0.00004
    - 
      params: '^(?=.*(?:norm|bn)).*$'
      weight_decay: 0.

  lr: 0.0004
  betas: [0.9, 0.999]
  weight_decay: 0.0001


# Increase to search for the optimal ema
epoches: 102 # 120 + 4n

## Our LR-Scheduler
flat_epoch: 49    # 4 + epoch // 2, e.g., 40 = 4 + 72 / 2
no_aug_epoch: 12

## Our DataAug
train_dataloader: 
  dataset: 
    transforms:
      policy:
        epoch: [4, 49, 90]   # list 

  collate_fn:
    mixup_epochs: [4, 49]
    stop_epoch: 90